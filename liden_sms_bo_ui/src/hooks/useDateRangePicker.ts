import { useState, useCallback, useMemo } from 'react'
import { DateRange } from 'react-day-picker'
import { format } from 'date-fns'

export interface DateRangePickerState {
  dateRange: DateRange | undefined
  isOpen: boolean
  formattedRange: string
}

export interface DateRangePickerActions {
  setDateRange: (range: DateRange | undefined) => void
  setIsOpen: (open: boolean) => void
  clearDateRange: () => void
  getFormattedDateRange: () => string
  getAPIDateParams: () => { start?: string; end?: string }
}

export interface UseDateRangePickerOptions {
  initialDateRange?: DateRange
  onDateRangeChange?: (range: DateRange | undefined) => void
  dateFormat?: string
}

export function useDateRangePicker(options: UseDateRangePickerOptions = {}) {
  const {
    initialDateRange,
    onDateRangeChange,
    dateFormat = 'yyyy-MM-dd'
  } = options

  const [dateRange, setDateRangeState] = useState<DateRange | undefined>(initialDateRange)
  const [isOpen, setIsOpen] = useState(false)

  const setDateRange = useCallback((range: DateRange | undefined) => {
    setDateRangeState(range)
    onDateRangeChange?.(range)
  }, [onDateRangeChange])

  const clearDateRange = useCallback(() => {
    setDateRange(undefined)
  }, [setDateRange])

  const getFormattedDateRange = useCallback(() => {
    if (!dateRange?.from) return ''
    
    if (dateRange.to) {
      return `${format(dateRange.from, 'MMM dd, yyyy')} - ${format(dateRange.to, 'MMM dd, yyyy')}`
    }
    
    return format(dateRange.from, 'MMM dd, yyyy')
  }, [dateRange])

  const getAPIDateParams = useCallback(() => {
    if (!dateRange?.from) return {}
    
    return {
      start: format(dateRange.from, dateFormat),
      end: dateRange.to ? format(dateRange.to, dateFormat) : format(dateRange.from, dateFormat)
    }
  }, [dateRange, dateFormat])

  const formattedRange = useMemo(() => getFormattedDateRange(), [getFormattedDateRange])

  const state: DateRangePickerState = {
    dateRange,
    isOpen,
    formattedRange
  }

  const actions: DateRangePickerActions = {
    setDateRange,
    setIsOpen,
    clearDateRange,
    getFormattedDateRange,
    getAPIDateParams
  }

  return {
    ...state,
    ...actions
  }
}

// Hook for managing multiple date range pickers
export function useMultipleDateRangePickers(count: number) {
  const pickers = useMemo(() => {
    return Array.from({ length: count }, () => useDateRangePicker())
  }, [count])

  return pickers
}

// Hook with predefined quick select options
export function useDateRangePickerWithQuickSelect(options: UseDateRangePickerOptions = {}) {
  const picker = useDateRangePicker(options)

  const quickSelectToday = useCallback(() => {
    const today = new Date()
    picker.setDateRange({ from: today, to: today })
  }, [picker])

  const quickSelectLast7Days = useCallback(() => {
    const today = new Date()
    const sevenDaysAgo = new Date(today)
    sevenDaysAgo.setDate(today.getDate() - 6)
    picker.setDateRange({ from: sevenDaysAgo, to: today })
  }, [picker])

  const quickSelectLast30Days = useCallback(() => {
    const today = new Date()
    const thirtyDaysAgo = new Date(today)
    thirtyDaysAgo.setDate(today.getDate() - 29)
    picker.setDateRange({ from: thirtyDaysAgo, to: today })
  }, [picker])

  const quickSelectThisMonth = useCallback(() => {
    const today = new Date()
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1)
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    picker.setDateRange({ from: firstDay, to: lastDay })
  }, [picker])

  const quickSelectLastMonth = useCallback(() => {
    const today = new Date()
    const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1)
    const lastDay = new Date(today.getFullYear(), today.getMonth(), 0)
    picker.setDateRange({ from: firstDay, to: lastDay })
  }, [picker])

  const quickSelectThisYear = useCallback(() => {
    const today = new Date()
    const firstDay = new Date(today.getFullYear(), 0, 1)
    const lastDay = new Date(today.getFullYear(), 11, 31)
    picker.setDateRange({ from: firstDay, to: lastDay })
  }, [picker])

  const quickSelectLastYear = useCallback(() => {
    const today = new Date()
    const firstDay = new Date(today.getFullYear() - 1, 0, 1)
    const lastDay = new Date(today.getFullYear() - 1, 11, 31)
    picker.setDateRange({ from: firstDay, to: lastDay })
  }, [picker])

  const quickSelectLastNMonths = useCallback((months: number) => {
    const today = new Date()
    const startDate = new Date(today)
    startDate.setMonth(today.getMonth() - months)
    picker.setDateRange({ from: startDate, to: today })
  }, [picker])

  const quickSelectLastNYears = useCallback((years: number) => {
    const today = new Date()
    const startDate = new Date(today)
    startDate.setFullYear(today.getFullYear() - years)
    picker.setDateRange({ from: startDate, to: today })
  }, [picker])

  return {
    ...picker,
    quickSelect: {
      today: quickSelectToday,
      last7Days: quickSelectLast7Days,
      last30Days: quickSelectLast30Days,
      thisMonth: quickSelectThisMonth,
      lastMonth: quickSelectLastMonth,
      thisYear: quickSelectThisYear,
      lastYear: quickSelectLastYear,
      lastNMonths: quickSelectLastNMonths,
      lastNYears: quickSelectLastNYears
    }
  }
}

export type DateRangePickerHook = ReturnType<typeof useDateRangePicker>
export type DateRangePickerWithQuickSelectHook = ReturnType<typeof useDateRangePickerWithQuickSelect>
