/**
 * React Hook for Dashboard Data
 * 
 * This hook provides a React-friendly interface to the dashboard data service,
 * managing subscriptions and providing convenient methods for components.
 */

import { useState, useEffect, useCallback } from 'react';
import { dashboardDataService, DashboardData, DashboardDataParams } from '@/services/dashboardDataService';

export interface UseDashboardDataOptions {
  autoFetch?: boolean;
  refreshInterval?: number; // in milliseconds
}

export interface UseDashboardDataReturn {
  data: DashboardData;
  fetchData: (params?: DashboardDataParams) => Promise<void>;
  refreshData: (params?: DashboardDataParams) => Promise<void>;
  clearData: () => void;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export function useDashboardData(
  params: DashboardDataParams = {},
  options: UseDashboardDataOptions = {}
): UseDashboardDataReturn {
  const { autoFetch = true, refreshInterval } = options;
  
  const [data, setData] = useState<DashboardData>(dashboardDataService.getCurrentData());

  // Subscribe to data service updates
  useEffect(() => {
    const unsubscribe = dashboardDataService.subscribe(setData);
    return unsubscribe;
  }, []);

  // Auto-fetch data on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      dashboardDataService.getDataWithRefresh(params);
    }
  }, [autoFetch, params.clientId, params.dateRange?.start, params.dateRange?.end]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(() => {
        if (!data.loading) {
          dashboardDataService.refreshData(params);
        }
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, params, data.loading]);

  // Memoized fetch function
  const fetchData = useCallback(async (fetchParams?: DashboardDataParams) => {
    const finalParams = { ...params, ...fetchParams };
    await dashboardDataService.fetchDashboardData(finalParams);
  }, [params]);

  // Memoized refresh function
  const refreshData = useCallback(async (refreshParams?: DashboardDataParams) => {
    const finalParams = { ...params, ...refreshParams };
    await dashboardDataService.refreshData(finalParams);
  }, [params]);

  // Memoized clear function
  const clearData = useCallback(() => {
    dashboardDataService.clearData();
  }, []);

  return {
    data,
    fetchData,
    refreshData,
    clearData,
    isLoading: data.loading,
    error: data.error,
    lastUpdated: data.lastUpdated,
  };
}

/**
 * Hook for wallet data specifically
 */
export function useWalletData(clientId?: string) {
  const { data, fetchData, refreshData, isLoading, error } = useDashboardData(
    { clientId },
    { autoFetch: true }
  );

  return {
    wallet: data.wallet,
    fetchWallet: fetchData,
    refreshWallet: refreshData,
    isLoading,
    error,
    lastUpdated: data.lastUpdated,
  };
}

/**
 * Hook for bulk usage data specifically
 */
export function useBulkUsageData(
  clientId?: string, 
  limit: number = 3, 
  status: number = 400
) {
  const { data, fetchData, refreshData, isLoading, error } = useDashboardData(
    { 
      clientId, 
      bulkUsageLimit: limit, 
      bulkUsageStatus: status 
    },
    { autoFetch: true }
  );

  return {
    bulkUsage: data.bulkUsage,
    fetchBulkUsage: fetchData,
    refreshBulkUsage: refreshData,
    isLoading,
    error,
    lastUpdated: data.lastUpdated,
  };
}

/**
 * Hook for dashboard stats data specifically
 */
export function useDashboardStatsData(
  clientId?: string,
  dateRange?: { start: string; end: string }
) {
  const { data, fetchData, refreshData, isLoading, error } = useDashboardData(
    { clientId, dateRange },
    { autoFetch: true }
  );

  return {
    dashboardStats: data.dashboardStats,
    fetchDashboardStats: fetchData,
    refreshDashboardStats: refreshData,
    isLoading,
    error,
    lastUpdated: data.lastUpdated,
  };
}

export default useDashboardData;
