/**
 * API Testing and Validation Suite
 * 
 * This file contains utilities for testing the authentication flow,
 * API integration, and token management system.
 */

import { LidenAPI } from './api-index';
import { <PERSON><PERSON>anager } from './utils';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './error-handler';
import type { LoginRequest } from './api-types';

export interface TestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

export interface TestSuite {
  name: string;
  tests: TestCase[];
}

export interface TestCase {
  name: string;
  test: () => Promise<TestResult>;
}

/**
 * API Test Runner
 */
export class ApiTestRunner {
  private static results: TestResult[] = [];

  /**
   * Run a single test case
   */
  static async runTest(testCase: TestCase): Promise<TestResult> {
    console.log(`Running test: ${testCase.name}`);
    
    try {
      const result = await testCase.test();
      console.log(`✅ ${testCase.name}:`, result.message);
      this.results.push(result);
      return result;
    } catch (error) {
      const failedResult: TestResult = {
        success: false,
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error
      };
      console.error(`❌ ${testCase.name}:`, failedResult.message);
      this.results.push(failedResult);
      return failedResult;
    }
  }

  /**
   * Run a test suite
   */
  static async runTestSuite(suite: TestSuite): Promise<TestResult[]> {
    console.log(`\n🧪 Running test suite: ${suite.name}`);
    console.log('='.repeat(50));
    
    const results: TestResult[] = [];
    
    for (const testCase of suite.tests) {
      const result = await this.runTest(testCase);
      results.push(result);
    }
    
    const passed = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log('\n📊 Test Results:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
    
    return results;
  }

  /**
   * Get all test results
   */
  static getResults(): TestResult[] {
    return [...this.results];
  }

  /**
   * Clear test results
   */
  static clearResults(): void {
    this.results = [];
  }
}

/**
 * Authentication Test Suite
 */
export const authTestSuite: TestSuite = {
  name: 'Authentication Flow Tests',
  tests: [
    {
      name: 'Cookie Manager - Set and Get Token',
      test: async (): Promise<TestResult> => {
        const testTokenData = {
          token: 'test-token-123',
          clientData: 'test-client-data',
          expires: 1,
          type: 'hour' as const,
          issuedAt: Date.now()
        };

        CookieManager.setAuthToken(testTokenData);
        const retrievedToken = CookieManager.getAuthToken();

        if (retrievedToken === testTokenData.token) {
          return {
            success: true,
            message: 'Token stored and retrieved successfully',
            data: { token: retrievedToken }
          };
        } else {
          return {
            success: false,
            message: 'Token mismatch after storage',
            data: { expected: testTokenData.token, actual: retrievedToken }
          };
        }
      }
    },
    {
      name: 'Cookie Manager - Token Validation',
      test: async (): Promise<TestResult> => {
        const isValid = CookieManager.isTokenValid();
        const expirationInfo = CookieManager.getTokenExpirationInfo();

        return {
          success: true,
          message: 'Token validation working',
          data: { isValid, expirationInfo }
        };
      }
    },
    {
      name: 'Cookie Manager - Clear Auth Data',
      test: async (): Promise<TestResult> => {
        CookieManager.clearAuthData();
        const token = CookieManager.getAuthToken();
        const userData = CookieManager.getUserData();

        if (!token && !userData) {
          return {
            success: true,
            message: 'Auth data cleared successfully'
          };
        } else {
          return {
            success: false,
            message: 'Auth data not fully cleared',
            data: { token, userData }
          };
        }
      }
    },
    {
      name: 'API Service - Token Header Injection',
      test: async (): Promise<TestResult> => {
        // Set a test token
        const testTokenData = {
          token: 'test-api-token-456',
          clientData: '',
          expires: 1,
          type: 'hour' as const,
          issuedAt: Date.now()
        };

        CookieManager.setAuthToken(testTokenData);

        // Import API service and check if token is set
        const { apiService } = await import('./api-index');
        const currentToken = apiService.getAuthToken();

        if (currentToken === testTokenData.token) {
          return {
            success: true,
            message: 'API service token injection working',
            data: { token: currentToken }
          };
        } else {
          return {
            success: false,
            message: 'API service token mismatch',
            data: { expected: testTokenData.token, actual: currentToken }
          };
        }
      }
    }
  ]
};

/**
 * Error Handling Test Suite
 */
export const errorHandlingTestSuite: TestSuite = {
  name: 'Error Handling Tests',
  tests: [
    {
      name: 'Error Handler - Parse Network Error',
      test: async (): Promise<TestResult> => {
        const networkError = new TypeError('Failed to fetch');
        const parsedError = ErrorHandler.parseApiError(networkError);

        if (parsedError.code === 'NETWORK_ERROR' && parsedError.isRetryable) {
          return {
            success: true,
            message: 'Network error parsed correctly',
            data: parsedError
          };
        } else {
          return {
            success: false,
            message: 'Network error not parsed correctly',
            data: parsedError
          };
        }
      }
    },
    {
      name: 'Error Handler - Parse Auth Error',
      test: async (): Promise<TestResult> => {
        const authError = {
          response: {
            status: 401,
            data: { statusDescription: 'Mandatory fields required!!' }
          }
        };
        const parsedError = ErrorHandler.parseApiError(authError);

        if (parsedError.code === 'MISSING_AUTH_TOKEN' && parsedError.isAuthError) {
          return {
            success: true,
            message: 'Auth error parsed correctly',
            data: parsedError
          };
        } else {
          return {
            success: false,
            message: 'Auth error not parsed correctly',
            data: parsedError
          };
        }
      }
    }
  ]
};

/**
 * Integration Test Suite
 */
export const integrationTestSuite: TestSuite = {
  name: 'API Integration Tests',
  tests: [
    {
      name: 'Wallet API - Test with Valid Token',
      test: async (): Promise<TestResult> => {
        // This test requires a valid token to be set
        const token = CookieManager.getAuthToken();
        
        if (!token) {
          return {
            success: false,
            message: 'No auth token available for testing'
          };
        }

        try {
          // Test the wallet endpoint that was mentioned in the requirements
          const { apiService } = await import('./api-index');
          const response = await apiService.get('/account/v1/view/wallet', { clientId: '46' });

          return {
            success: response.success,
            message: response.success ? 'Wallet API call successful' : 'Wallet API call failed',
            data: response
          };
        } catch (error) {
          return {
            success: false,
            message: 'Wallet API call threw error',
            error
          };
        }
      }
    }
  ]
};

/**
 * Utility function to run all test suites
 */
export async function runAllTests(): Promise<void> {
  console.log('🚀 Starting comprehensive API tests...\n');
  
  ApiTestRunner.clearResults();
  
  await ApiTestRunner.runTestSuite(authTestSuite);
  await ApiTestRunner.runTestSuite(errorHandlingTestSuite);
  await ApiTestRunner.runTestSuite(integrationTestSuite);
  
  const allResults = ApiTestRunner.getResults();
  const totalPassed = allResults.filter(r => r.success).length;
  const totalFailed = allResults.filter(r => !r.success).length;
  
  console.log('\n🏁 Overall Test Summary:');
  console.log('='.repeat(50));
  console.log(`✅ Total Passed: ${totalPassed}`);
  console.log(`❌ Total Failed: ${totalFailed}`);
  console.log(`📈 Overall Success Rate: ${((totalPassed / allResults.length) * 100).toFixed(1)}%`);
}

/**
 * Quick test function for development
 */
export async function quickTest(): Promise<void> {
  console.log('⚡ Running quick authentication test...');
  
  const result = await ApiTestRunner.runTest(authTestSuite.tests[0]);
  
  if (result.success) {
    console.log('✅ Quick test passed!');
  } else {
    console.log('❌ Quick test failed!');
  }
}
