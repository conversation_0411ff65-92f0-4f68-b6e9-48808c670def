// Extended API methods for Liden services
import { apiService, endpoints } from './api';
import {
  ApiResponse,
  PaginationParams,
  DateRangeParams,
  ExportParams,
  USSDGatewayParams,
  USSDApp,
  USSDAppsResponse,
  USSDType,
  USSDTypesResponse,
  USSDAccessPoint,
  USSDCreateAppRequest,
  USSDConfigureAccessPointRequest,
  USSDAppsParams,
  USSDTypesParams,
  USSDAccessPointsParams,
  SMSNetwork,
  SMSNetworksResponse,
  SMSNetworksParams,
  SenderIdRequest,
  SenderIdsResponse,
  SenderIdsParams,
  Contact,
  ContactEditRequest,
  ContactListParams,
  AddUserRequest,
  EditUserRequest,
  UserPermissionsRequest,
  MpesaConfigRequest,
  ClientWallet,
  AuditLog,
  DashboardStats,
  WalletResponse,
  BulkUsageResponse,
  BulkUsageParams,
  DashboardStatsResponse,
  DashboardStatsParams,
  BulkMessagesResponse,
  BulkMessagesParams,
  ContactGroupsResponse,
  ContactGroupsParams,
  ContactGroupUpdateRequest,
  UtilityTransaction,
  UtilityTransactionParams,
  UtilityTransactionsResponse,
  SchedulerExecuteRequest,
  ATCallbackData,
  C2BConfirmationData,
  MessageReport,
  MessageReportParams,
  VoiceType,
  BulkRateCard,
  BulkRateCardResponse,
  BulkRateCardParams,
  Invoice,
  InvoiceResponse,
  InvoiceParams,
  User,
  UserRole,
  UserResponse,
  UserRoleResponse,
  UserParams,
  CreateUserRequest,
  UpdateUserRequest
} from './api-types';

// ============================================================================
// USSD API METHODS
// ============================================================================

export class USSDAPI {
  /**
   * Safaricom USSD gateway
   */
  static async safaricomGateway(params: USSDGatewayParams): Promise<ApiResponse<any>> {
    return apiService.get(endpoints.ussd.safaricomGateway, params);
  }

  /**
   * View USSD types
   */
  // static async getTypes(params?: USSDTypesParams): Promise<ApiResponse<USSDTypesResponse>> {
  static async getTypes(params?: USSDTypesParams): Promise<any> {
    return apiService.get(endpoints.ussd.types, params);
  }

  /**
   * View access points
   */
  static async getAccessPoints(params?: USSDAccessPointsParams): Promise<any> {
    return apiService.get(endpoints.ussd.accessPoints, params);
  }

  /**
   * View USSD apps
   */
  static async getApps(params?: USSDAppsParams): Promise<any> {
    return apiService.get(endpoints.ussd.apps, params);
  }

  /**
   * Create new USSD app
   */
  static async createApp(request: USSDCreateAppRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.ussd.createApp, request);
  }

  /**
   * Configure access point
   */
  static async configureAccessPoint(request: USSDConfigureAccessPointRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.ussd.configureAccessPoint, request);
  }
}

// ============================================================================
// SMS NETWORKS API METHODS
// ============================================================================

export class SMSNetworksAPI {
  /**
   * Get SMS networks with date filtering
   */
  // static async getNetworks(params?: SMSNetworksParams): Promise<ApiResponse<SMSNetworksResponse>> {
  static async getNetworks(params?: SMSNetworksParams): Promise<any> {
    return apiService.get(endpoints.sms.networks, params);
  }
}

// ============================================================================
// CONTACT MANAGEMENT API METHODS
// ============================================================================

export class ContactAPI {
  /**
   * View contacts
   */
  static async getContacts(params?: ContactListParams): Promise<any> {
    const response = await apiService.get(endpoints.contact.view, params);

    const data = {
      status: response.data.data?.code,
      message: response.data.data?.message ?? "",
      total_count: response.data.data.data[0]?.total_count ?? 0,
      data: response.data.data?.data ?? [],
    }
    return data;
  }

  /**
   * Edit mobile entry
   */
  static async editEntry(contactId: string, request: ContactEditRequest): Promise<any> {
    return apiService.post(endpoints.contact.edit.replace('{contactId}', contactId), request);
  }

  /**
   * Get contact groups
   */
  static async getGroups(params?: ContactGroupsParams): Promise<any> {
    const response = await apiService.get(endpoints.contact.groups, params);
    const data = {
      status: response.data.data?.code,
      message: response.data.data?.message ?? "",
      total_count: response.data.data.data[0]?.total_count ?? 0,
      data: response.data.data?.data ?? [],
    }
    return data;
  }

  /**
   * Update contact group
   */
  static async updateGroup(groupId: string, request: ContactGroupUpdateRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.contact.updateGroup.replace('{groupId}', groupId), request);
  }
}

// ============================================================================
// CLIENT MANAGEMENT API METHODS
// ============================================================================

export class ClientAPI {
  /**
   * Add system users
   */
  static async addUser(request: AddUserRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.addUser, request);
  }

  /**
   * Edit system user
   */
  static async editUser(request: EditUserRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.editUser, request);
  }

  /**
   * Add client permissions
   */
  static async addPermissions(request: UserPermissionsRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.addPermissions, request);
  }

  /**
   * Configure M-pesa payouts
   */
  static async configureMpesa(request: MpesaConfigRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.configureMpesa, request);
  }

  /**
   * Edit M-pesa paybill
   */
  static async editMpesa(request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.editMpesa, request);
  }

  /**
   * Activate/deactivate client account
   */
  static async activateAccount(request: { status: string; clientId: string; reason: string }): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.activateAccount, request);
  }

  /**
   * View configured countries
   */
  static async getCountries(): Promise<any> {
    return apiService.get(endpoints.client.countries);
  }

  /**
   * View client wallet
   */
  static async getWallet(clientId?: string): Promise<ApiResponse<ClientWallet>> {
    return apiService.get<ClientWallet>(endpoints.client.wallet, { clientId });
  }

  /**
   * View user audit logs
   */
  static async getAuditLogs(params?: PaginationParams & DateRangeParams & ExportParams & {
    filter?: string;
    userId?: string;
  }): Promise<ApiResponse<AuditLog[]>> {
    return apiService.get<AuditLog[]>(endpoints.client.auditLogs, params);
  }

  /**
   * View user invoices
   */
  static async getInvoices(params?: PaginationParams & DateRangeParams & ExportParams & {
    userMapId?: string;
    invoiceId?: string;
    invoiceAmount?: string;
    clientId?: string;
    serviceId?: string;
    invoiceReference?: string;
  }): Promise<ApiResponse<Invoice[]>> {
    return apiService.get<Invoice[]>(endpoints.client.invoices, params);
  }

  /**
   * View dashboard stats
   */
  static async getDashboardStats(params: DateRangeParams & { userMapId?: string }): Promise<ApiResponse<DashboardStats>> {
    return apiService.get<DashboardStats>(endpoints.client.dashboardStats, params);
  }

  /**
   * Get enhanced wallet information
   */
  static async getWalletInfo(clientId?: string): Promise<ApiResponse<WalletResponse>> {
    return apiService.get<WalletResponse>(endpoints.client.wallet, { clientId });
  }

  /**
   * Get bulk SMS usage statistics
   */
  static async getBulkUsage(params?: BulkUsageParams): Promise<ApiResponse<BulkUsageResponse[]>> {
    return apiService.get<BulkUsageResponse[]>(endpoints.client.bulkUsage, params);
  }

  /**
   * Get enhanced dashboard statistics
   */
  static async getDashboardStatistics(params: DashboardStatsParams): Promise<ApiResponse<DashboardStatsResponse>> {
    return apiService.get<DashboardStatsResponse>(endpoints.client.dashboardStats, params);
  }

  /**
   * Get bulk messages with filtering and pagination
   */
  static async getBulkMessages(params?: BulkMessagesParams): Promise<any> {
    return apiService.get(endpoints.client.bulkMessages, params);
  }

  /**
   * Get sender IDs
   */
  static async getSenderIds(params?: any): Promise<any> {
    return apiService.get(endpoints.client.senderIds, params);
  }
}



// ============================================================================
// VOICE API METHODS
// ============================================================================

export class VoiceAPI {
  /**
   * View VOIP types
   */
  static async getTypes(): Promise<any> {
    return apiService.get(endpoints.voice.types);
  }
}

// ============================================================================
// SCHEDULER API METHODS
// ============================================================================

export class SchedulerAPI {
  /**
   * Execute services
   */
  static async execute(request: SchedulerExecuteRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.scheduler.execute, request);
  }
}

// ============================================================================
// WEBHOOK API METHODS
// ============================================================================

export class WebhookAPI {
  /**
   * AT callback validation
   */
  static async atCallback(data: ATCallbackData): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.webhooks.atCallback, data);
  }

  /**
   * C2B confirmation
   */
  static async c2bConfirmation(data: C2BConfirmationData): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.webhooks.c2bConfirmation, data);
  }
}

// ============================================================================
// PREMIUM CONTENT API METHODS
// ============================================================================

export class PremiumContentAPI {
  /**
   * View subscription outbox
   */
  static async getMessageReports(params?: MessageReportParams): Promise<ApiResponse<MessageReport[]>> {
    return apiService.get<MessageReport[]>(endpoints.subscription.messageReports, params);
  }
}

// ============================================================================
// BULK RATE CARD API METHODS
// ============================================================================

export class BulkRateCardAPI {
  /**
   * Get bulk rate cards
   */
  static async getBulkRateCards(params?: BulkRateCardParams): Promise<ApiResponse<BulkRateCardResponse>> {
    return apiService.get<BulkRateCardResponse>(endpoints.bulkRateCard.view, params);
  }
}

// ============================================================================
// INVOICE API METHODS
// ============================================================================

export class InvoiceAPI {
  /**
   * Get invoices
   */
  static async getInvoices(params?: InvoiceParams): Promise<ApiResponse<InvoiceResponse>> {
    return apiService.get<InvoiceResponse>(endpoints.invoice.view, params);
  }
}

// ============================================================================
// USER MANAGEMENT API METHODS
// ============================================================================

export class UserManagementAPI {
  /**
   * Get all users
   */
  static async getUsers(params?: UserParams): Promise<ApiResponse<UserResponse>> {
    return apiService.get<UserResponse>(endpoints.userManagement.users, params);
  }

  /**
   * Get user roles
   */
  static async getUserRoles(): Promise<ApiResponse<UserRoleResponse>> {
    return apiService.get<UserRoleResponse>(endpoints.userManagement.roles);
  }

  /**
   * Create new user
   */
  static async createUser(userData: CreateUserRequest): Promise<ApiResponse<any>> {
    return apiService.post<any>(endpoints.userManagement.create, userData);
  }

  /**
   * Update user
   */
  static async updateUser(userData: UpdateUserRequest): Promise<ApiResponse<any>> {
    return apiService.put<any>(endpoints.userManagement.update, userData);
  }

  /**
   * Delete user
   */
  static async deleteUser(userId: string): Promise<ApiResponse<any>> {
    return apiService.delete<any>(endpoints.userManagement.delete.replace('{userId}', userId));
  }
}

// Export all API classes for easy access
export {
  // From main api.ts file
  AuthAPI,
  SMSAPI,
  SurveyAPI,
  UtilityAPI
} from './api';

// Export extended API classes - DashboardAPI methods are now part of ClientAPI
// All classes are already exported individually above
