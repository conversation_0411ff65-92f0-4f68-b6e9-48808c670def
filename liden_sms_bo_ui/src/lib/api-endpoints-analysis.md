# Liden API Endpoints Analysis

Based on the Postman collection, here are all the available API endpoints organized by service categories:

## Authentication APIs
- **POST** `/account/v1/grant_access` - User login
- **POST** `/account/v1/logout` - User logout  
- **POST** `/account/v1/forgot-password` - Forgot password
- **POST** `/account/v1/reset-password` - Reset password
- **GET** `/account/v1/profile` - Get user profile

## Survey APIs
- **GET** `/survey/v1/view/active_channels` - Get survey active channels
- **GET** `/survey/v1/view/charges` - Get survey charges
- **POST** `/survey/v1/create` - Create customer survey
- **POST** `/survey/v1/add/respondents/{appId}` - Add respondents
- **POST** `/survey/v1/edit/{appId}` - Edit survey settings
- **POST** `/survey/v1/stop/{appId}` - Stop survey
- **POST** `/survey/v1/send_sms/{settingId}` - Send SMS blast
- **GET** `/survey/v1/view/responses/{appId}` - View survey responses
- **GET** `/survey/v1/view/all` - View all survey applications
- **GET** `/survey/v1/view/incentives_trxn/{appId}` - View incentive rewards transactions
- **GET** `/survey/v1/view/question_types` - Get question types
- **GET** `/survey/v1/view/incentives` - Get incentive types
- **POST** `/survey/v1/execute` - Execute survey app

## SMS APIs
- **POST** `/sms/v1/blast/send_group` - Send bulk SMS blast/query
- **GET** `/sms/v1/view/outbox` - View SMS outbox
- **GET** `/sms/v1/analytics` - SMS analytics
- **GET** `/sms/v1/view/messages` - View SMS messages
- **GET** `/sms/v1/blacklist` - SMS blacklist
- **POST** `/sms/v1/premium` - Premium SMS
- **POST** `/sms/v1/shortcode` - Shortcode SMS
- **POST** `/sms/v1/alphanumeric` - Alphanumeric SMS

## Utility/Airtime APIs
- **POST** `/bill/v1/send_airtime` - Send single airtime
- **POST** `/bill/v1/bulk_airtime` - Send bulk airtime
- **POST** `/bill/v1/approval` - Bulk service approval
- **POST** `/bill/v1/mpesa_payout` - M-pesa payouts
- **POST** `/bill/v1/mpesa_b2b_payout` - M-pesa B2B payouts
- **GET** `/bill/v1/status/mpesa_payout` - Query M-pesa transaction status
- **POST** `/activate/v1/airtime` - Activate airtime service
- **GET** `/account/v1/view/utilities` - View utility reports
- **GET** `/account/v1/view/utility/transactions/{id}` - View utility transactions

## USSD APIs
- **GET** `/ussd/v1/gw/ke/safaricom` - Safaricom USSD gateway
- **GET** `/ussd/v1/view/types` - View USSD types
- **GET** `/ussd/v1/view/access_points` - View access points
- **GET** `/ussd/v1/view/apps` - View USSD apps
- **POST** `/ussd/v1/create_app` - Create new USSD app
- **POST** `/ussd/v1/configure/access_point` - Configure access point

## Contact Management APIs
- **GET** `/contact/v1/view/contacts` - View contacts
- **POST** `/contact/v1/edit_entry/{contactId}` - Edit mobile entry

## Client Management APIs
- **POST** `/account/v1/add_users` - Add system users
- **POST** `/account/v1/edit_user` - Edit system user
- **POST** `/account/v1/add/user_permissions` - Add client permissions
- **POST** `/account/v1/configure/add_paybills` - Configure M-pesa payouts
- **POST** `/account/v1/configure/edit_paybills` - Edit M-pesa paybill
- **POST** `/activate/v1/client_account` - Activate/deactivate client account
- **GET** `/account/v1/view/countries` - View configured countries
- **GET** `/account/v1/view/wallet` - View client wallet
- **GET** `/account/v1/view/user/audit_logs` - View user audit logs
- **GET** `/account/v1/view/invoices` - View user invoices
- **GET** `/account/v1/view/dashboard_stats` - View dashboard stats

## Voice APIs
- **GET** `/voice/v1/view/types` - View VOIP types

## Scheduler APIs
- **POST** `/scheduler/v1/execute` - Execute services

## Webhook APIs
- **POST** `/webhook/v1/at_callback/validate` - AT validation callback
- **POST** `/webhook/v1/c2b_confirmation` - C2B M-pesa payments callback

## Premium Content APIs
- **GET** `/subscription/v1/view/message_reports` - View subscription outbox

## Common Request/Response Patterns

### Authentication Headers
- `X-Authorization-Key`: Required for authenticated requests
- `X-Requested-With`: "XMLHttpRequest"
- `Content-Type`: "application/json"

### Common Query Parameters
- `sort`, `offset`, `limit` - Pagination
- `start`, `end` - Date filtering
- `export` - Export functionality
- `clientId` - Client filtering

### Response Structure
Most APIs return responses in this format:
```json
{
  "data": {},
  "success": boolean,
  "message": "string",
  "errors": ["string"]
}
```
