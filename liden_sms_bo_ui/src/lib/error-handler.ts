/**
 * Comprehensive error handling utilities for API calls and authentication
 */

export interface ApiError {
  code: string;
  message: string;
  statusDescription?: string;
  statusCode?: number;
  isAuthError?: boolean;
  isRetryable?: boolean;
}

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
}

/**
 * Error handler class for API responses
 */
export class ErrorHandler {
  private static readonly DEFAULT_RETRY_CONFIG: RetryConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 2
  };

  /**
   * Parse and categorize API errors
   */
  static parseApiError(error: any): ApiError {
    // Handle fetch/network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed. Please check your internet connection.',
        isRetryable: true
      };
    }

    // Handle timeout errors
    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return {
        code: 'TIMEOUT_ERROR',
        message: 'Request timed out. Please try again.',
        isRetryable: true
      };
    }

    // Handle API response errors
    if (error.response) {
      const { status, data } = error.response;
      
      // Authentication errors
      if (status === 401 || status === 403) {
        return {
          code: 'AUTH_ERROR',
          message: 'Authentication failed. Please log in again.',
          statusCode: status,
          isAuthError: true,
          isRetryable: false
        };
      }

      // Handle specific Liden API error format
      if (data && typeof data === 'object') {
        if (data.statusDescription === 'Mandatory fields required!!') {
          return {
            code: 'MISSING_AUTH_TOKEN',
            message: 'Authentication token is missing or invalid. Please log in again.',
            statusDescription: data.statusDescription,
            statusCode: status,
            isAuthError: true,
            isRetryable: false
          };
        }

        if (data.code === 'Error') {
          return {
            code: 'API_ERROR',
            message: data.statusDescription || data.message || 'An error occurred',
            statusDescription: data.statusDescription,
            statusCode: status,
            isRetryable: status >= 500
          };
        }
      }

      // Generic HTTP errors
      return {
        code: 'HTTP_ERROR',
        message: `HTTP ${status}: ${this.getHttpErrorMessage(status)}`,
        statusCode: status,
        isRetryable: status >= 500
      };
    }

    // Handle string errors
    if (typeof error === 'string') {
      return {
        code: 'UNKNOWN_ERROR',
        message: error,
        isRetryable: false
      };
    }

    // Handle Error objects
    if (error instanceof Error) {
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message,
        isRetryable: false
      };
    }

    // Fallback for unknown error types
    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred',
      isRetryable: false
    };
  }

  /**
   * Get user-friendly HTTP error messages
   */
  private static getHttpErrorMessage(status: number): string {
    switch (status) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Authentication required. Please log in.';
      case 403:
        return 'Access denied. You do not have permission.';
      case 404:
        return 'Resource not found.';
      case 429:
        return 'Too many requests. Please wait and try again.';
      case 500:
        return 'Server error. Please try again later.';
      case 502:
        return 'Bad gateway. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again later.';
      default:
        return 'An error occurred. Please try again.';
    }
  }

  /**
   * Retry function with exponential backoff
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const { maxRetries, retryDelay, backoffMultiplier } = {
      ...this.DEFAULT_RETRY_CONFIG,
      ...config
    };

    let lastError: any;
    let delay = retryDelay;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        const apiError = this.parseApiError(error);

        // Don't retry if it's not a retryable error
        if (!apiError.isRetryable || attempt === maxRetries) {
          throw error;
        }

        console.warn(`Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, apiError.message);
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= backoffMultiplier;
      }
    }

    throw lastError;
  }

  /**
   * Handle authentication errors by clearing tokens and redirecting
   */
  static handleAuthError(error: ApiError): void {
    if (error.isAuthError) {
      // Clear authentication data
      import('./utils').then(({ CookieManager }) => {
        CookieManager.clearAuthData();
      });

      // Redirect to login page
      if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
  }

  /**
   * Show user-friendly error messages
   */
  static showErrorMessage(error: ApiError): void {
    // You can integrate with your toast/notification system here
    console.error('API Error:', error.message);
    
    // Example: Show toast notification
    // toast.error(error.message);
  }

  /**
   * Comprehensive error handler for API calls
   */
  static async handleApiCall<T>(
    operation: () => Promise<T>,
    options: {
      showErrorMessage?: boolean;
      retryConfig?: Partial<RetryConfig>;
      handleAuthErrors?: boolean;
    } = {}
  ): Promise<T> {
    const {
      showErrorMessage = true,
      retryConfig = {},
      handleAuthErrors = true
    } = options;

    try {
      return await this.withRetry(operation, retryConfig);
    } catch (error) {
      const apiError = this.parseApiError(error);

      if (handleAuthErrors) {
        this.handleAuthError(apiError);
      }

      if (showErrorMessage) {
        this.showErrorMessage(apiError);
      }

      throw apiError;
    }
  }
}

/**
 * Utility function for making API calls with error handling
 */
export const apiCall = <T>(
  operation: () => Promise<T>,
  options?: Parameters<typeof ErrorHandler.handleApiCall>[1]
): Promise<T> => {
  return ErrorHandler.handleApiCall(operation, options);
};

/**
 * Specific error types for different scenarios
 */
export class AuthenticationError extends Error {
  constructor(message: string = 'Authentication failed') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class NetworkError extends Error {
  constructor(message: string = 'Network connection failed') {
    super(message);
    this.name = 'NetworkError';
  }
}

export class ValidationError extends Error {
  constructor(message: string = 'Validation failed') {
    super(message);
    this.name = 'ValidationError';
  }
}

export class ServerError extends Error {
  constructor(message: string = 'Server error occurred') {
    super(message);
    this.name = 'ServerError';
  }
}
