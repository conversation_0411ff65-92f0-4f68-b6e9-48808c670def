import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  BarChart3, 
  Users, 
  MessageSquare,
  TrendingUp,
  Download,
  Filter,
  Eye,
  PieChart,
  Activity
} from "lucide-react";

const SurveyStats = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);

  // Mock data for surveys
  const surveys = [
    {
      id: "1",
      title: "Customer Satisfaction Survey",
      responses: 1250,
      completion: 87,
      avgRating: 4.2,
      status: "Active",
      created: "2024-01-10",
      lastResponse: "2024-01-16 14:30",
    },
    {
      id: "2",
      title: "Product Feedback Survey",
      responses: 890,
      completion: 92,
      avgRating: 3.8,
      status: "Active",
      created: "2024-01-08",
      lastResponse: "2024-01-16 13:45",
    },
    {
      id: "3",
      title: "Service Quality Assessment",
      responses: 2100,
      completion: 78,
      avgRating: 4.5,
      status: "Completed",
      created: "2024-01-01",
      lastResponse: "2024-01-15 16:20",
    },
  ];

  const columns = [
    {
      accessorKey: "title",
      header: "Survey Title",
      cell: ({ row }: any) => (
        <div className="font-medium">{row.getValue("title")}</div>
      ),
    },
    {
      accessorKey: "responses",
      header: "Responses",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <Users className="h-4 w-4 text-blue-500" />
          <span className="font-medium">{row.getValue("responses").toLocaleString()}</span>
        </div>
      ),
    },
    {
      accessorKey: "completion",
      header: "Completion Rate",
      cell: ({ row }: any) => {
        const completion = row.getValue("completion");
        return (
          <div className="flex items-center space-x-2">
            <Progress value={completion} className="w-16" />
            <span className="text-sm font-medium">{completion}%</span>
          </div>
        );
      },
    },
    {
      accessorKey: "avgRating",
      header: "Avg Rating",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-1">
          <span className="font-medium">{row.getValue("avgRating")}</span>
          <span className="text-yellow-500">★</span>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        return (
          <Badge 
            variant={status === "Active" ? "default" : status === "Completed" ? "secondary" : "destructive"}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "lastResponse",
      header: "Last Response",
      cell: ({ row }: any) => (
        <span className="text-sm text-muted-foreground">{row.getValue("lastResponse")}</span>
      ),
    },
  ];

  // Mock response data for charts
  const responseData = [
    { question: "Overall Satisfaction", excellent: 45, good: 35, average: 15, poor: 5 },
    { question: "Product Quality", excellent: 52, good: 28, average: 15, poor: 5 },
    { question: "Customer Service", excellent: 38, good: 42, average: 15, poor: 5 },
    { question: "Value for Money", excellent: 35, good: 40, average: 20, poor: 5 },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <BarChart3 className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Survey Statistics</h1>
            <p className="text-muted-foreground">View and analyze survey responses and insights</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Surveys</p>
                <p className="text-2xl font-bold">12</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Responses</p>
                <p className="text-2xl font-bold">4,240</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Completion</p>
                <p className="text-2xl font-bold">85.7%</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Rating</p>
                <p className="text-2xl font-bold">4.2 ★</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="surveys">All Surveys</TabsTrigger>
          <TabsTrigger value="responses">Response Analysis</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Survey Performance</CardTitle>
                <CardDescription>Completion rates for active surveys</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {surveys.filter(s => s.status === "Active").map((survey) => (
                  <div key={survey.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{survey.title}</span>
                      <span className="text-sm text-muted-foreground">{survey.completion}%</span>
                    </div>
                    <Progress value={survey.completion} className="h-2" />
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{survey.responses} responses</span>
                      <span>{survey.avgRating} ★ avg rating</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Response Distribution</CardTitle>
                <CardDescription>Overall satisfaction ratings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { label: "Excellent", value: 42, color: "bg-green-500" },
                    { label: "Good", value: 35, color: "bg-blue-500" },
                    { label: "Average", value: 18, color: "bg-yellow-500" },
                    { label: "Poor", value: 5, color: "bg-red-500" },
                  ].map((item) => (
                    <div key={item.label} className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                      <span className="text-sm font-medium w-16">{item.label}</span>
                      <Progress value={item.value} className="flex-1 h-2" />
                      <span className="text-sm text-muted-foreground w-8">{item.value}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="surveys" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>All Surveys</CardTitle>
                  <CardDescription>Manage and view all survey campaigns</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={surveys} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="responses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Response Analysis</CardTitle>
              <CardDescription>Detailed breakdown of survey responses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {responseData.map((question, index) => (
                  <div key={index} className="space-y-3">
                    <h4 className="font-medium">{question.question}</h4>
                    <div className="grid grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{question.excellent}%</div>
                        <div className="text-sm text-muted-foreground">Excellent</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{question.good}%</div>
                        <div className="text-sm text-muted-foreground">Good</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600">{question.average}%</div>
                        <div className="text-sm text-muted-foreground">Average</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{question.poor}%</div>
                        <div className="text-sm text-muted-foreground">Poor</div>
                      </div>
                    </div>
                    <div className="flex h-4 rounded-full overflow-hidden">
                      <div className="bg-green-500" style={{ width: `${question.excellent}%` }}></div>
                      <div className="bg-blue-500" style={{ width: `${question.good}%` }}></div>
                      <div className="bg-yellow-500" style={{ width: `${question.average}%` }}></div>
                      <div className="bg-red-500" style={{ width: `${question.poor}%` }}></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Response Trends</CardTitle>
                <CardDescription>Survey response volume over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <PieChart className="h-12 w-12 mx-auto mb-2" />
                    <p>Chart visualization would go here</p>
                    <p className="text-sm">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Satisfaction Trends</CardTitle>
                <CardDescription>Average satisfaction ratings over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <TrendingUp className="h-12 w-12 mx-auto mb-2" />
                    <p>Trend chart would go here</p>
                    <p className="text-sm">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SurveyStats;
