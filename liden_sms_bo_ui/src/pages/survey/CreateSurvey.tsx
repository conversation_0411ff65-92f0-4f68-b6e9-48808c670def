import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Plus,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Check
} from "lucide-react";
import { LidenAPI } from "@/lib/api-index";
import { ContactGroup, Country } from "@/lib/api-types";

// Wizard step types
type WizardStep = 'details' | 'questions' | 'settings' | 'confirm';

// Question types based on the UI
interface Question {
  id: string;
  type: 'linear-scale-agree' | 'multiple-choice' | 'text' | 'rating' | 'yes-no';
  question: string;
  options?: string[];
  required: boolean;
}

// Survey form data structure
interface SurveyFormData {
  // Survey Details
  surveyName: string;
  surveyType: string;
  startDate: string;
  endDate: string;
  channel: string;
  shortCode: string;
  selectRecipient: string;
  
  // Questions
  questions: Question[];
  
  // Settings
  autoReminder: boolean;
  optinMessage: string;
  optoutMessage: string;
  savePoint: boolean;
  whitelist: boolean;
  reward: boolean;
}

const CreateSurvey = () => {
  const [currentStep, setCurrentStep] = useState<WizardStep>('details');
  const [contactGroups, setContactGroups] = useState<ContactGroup[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState<SurveyFormData>({
    surveyName: '',
    surveyType: 'Data Collection',
    startDate: '',
    endDate: '',
    channel: '',
    shortCode: '',
    selectRecipient: '',
    questions: [],
    autoReminder: false,
    optinMessage: '',
    optoutMessage: '',
    savePoint: false,
    whitelist: false,
    reward: false,
  });

  // Fetch contact groups and countries on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [groupsResponse, countriesResponse] = await Promise.all([
          LidenAPI.contact.getGroups({ limit: 100 }),
          LidenAPI.client.getCountries()
        ]);
        
        if (groupsResponse.success && groupsResponse.data) {
          const responseData = groupsResponse.data as any;
          let groupsData: any[] = []

          if (responseData?.data?.data && Array.isArray(responseData.data.data)) {
            groupsData = responseData.data.data
          } else if (responseData?.data && Array.isArray(responseData.data)) {
            groupsData = responseData.data
          } else if (Array.isArray(responseData)) {
            groupsData = responseData
          }

          setContactGroups(groupsData);
        } else {
          setContactGroups([]);
        }

        if (countriesResponse.success && countriesResponse.data) {
          const responseData = countriesResponse.data as any;
          let countriesData: any[] = []

          if (responseData?.data?.data && Array.isArray(responseData.data.data)) {
            countriesData = responseData.data.data
          } else if (responseData?.data && Array.isArray(responseData.data)) {
            countriesData = responseData.data
          } else if (Array.isArray(responseData)) {
            countriesData = responseData
          }

          setCountries(countriesData);
        } else {
          setCountries([]);
        }
      } catch (error) {
        console.error('Failed to fetch data:', error);
      }
    };
    
    fetchData();
  }, []);

  // Wizard navigation
  const steps: WizardStep[] = ['details', 'questions', 'settings', 'confirm'];
  const currentStepIndex = steps.indexOf(currentStep);
  
  const nextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStep(steps[currentStepIndex + 1]);
    }
  };
  
  const prevStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStep(steps[currentStepIndex - 1]);
    }
  };
  
  const goToStep = (step: WizardStep) => {
    setCurrentStep(step);
  };

  // Question management
  const addQuestion = () => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      type: 'linear-scale-agree',
      question: '',
      required: false,
    };
    setFormData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }));
  };

  const updateQuestion = (id: string, updates: Partial<Question>) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q => 
        q.id === id ? { ...q, ...updates } : q
      )
    }));
  };

  const deleteQuestion = (id: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== id)
    }));
  };

  // Form field updates
  const updateFormData = (field: keyof SurveyFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Submit survey
  const handleSubmit = async () => {
    try {
      setLoading(true);
      // Here you would call the survey creation API
      console.log('Creating survey:', formData);
      // await LidenAPI.survey.create(formData);
    } catch (error) {
      console.error('Failed to create survey:', error);
    } finally {
      setLoading(false);
    }
  };

  // Step 1: Survey Details
  const renderSurveyDetails = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-white mb-2">Survey Details</h2>
        <p className="text-gray-400">Enter basic survey details</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-4">
          <div>
            <Label className="text-white">Survey Name</Label>
            <Input
              placeholder="Customer Feedback"
              value={formData.surveyName}
              onChange={(e) => updateFormData('surveyName', e.target.value)}
              className="bg-slate-700 border-slate-600 text-white"
            />
          </div>
          
          <div>
            <Label className="text-white">Start Date</Label>
            <Input
              type="date"
              value={formData.startDate}
              onChange={(e) => updateFormData('startDate', e.target.value)}
              className="bg-slate-700 border-slate-600 text-white"
            />
          </div>
          
          <div>
            <Label className="text-white">Channel</Label>
            <Select value={formData.channel} onValueChange={(value) => updateFormData('channel', value)}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Select channel" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="sms" className="text-white">SMS</SelectItem>
                <SelectItem value="email" className="text-white">Email</SelectItem>
                <SelectItem value="ussd" className="text-white">USSD</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Right Column */}
        <div className="space-y-4">
          <div>
            <Label className="text-white">Survey</Label>
            <Select value={formData.surveyType} onValueChange={(value) => updateFormData('surveyType', value)}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="Data Collection" className="text-white">Data Collection</SelectItem>
                <SelectItem value="Feedback" className="text-white">Feedback</SelectItem>
                <SelectItem value="Research" className="text-white">Research</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label className="text-white">End Date</Label>
            <Input
              type="date"
              value={formData.endDate}
              onChange={(e) => updateFormData('endDate', e.target.value)}
              className="bg-slate-700 border-slate-600 text-white"
            />
          </div>
          
          <div>
            <Label className="text-white">ShortCode</Label>
            <Select value={formData.shortCode} onValueChange={(value) => updateFormData('shortCode', value)}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Select shortcode" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="20050" className="text-white">20050</SelectItem>
                <SelectItem value="20051" className="text-white">20051</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      
      <div>
        <Label className="text-white">Select Recipient</Label>
        <Select value={formData.selectRecipient} onValueChange={(value) => updateFormData('selectRecipient', value)}>
          <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
            <SelectValue placeholder="Choose contact group" />
          </SelectTrigger>
          <SelectContent className="bg-slate-800 border-slate-700">
            {Array.isArray(contactGroups) && contactGroups.map((group) => (
              <SelectItem key={group.list_id} value={group.list_id} className="text-white">
                {group.group_name}
              </SelectItem>
            ))}
            {(!Array.isArray(contactGroups) || contactGroups.length === 0) && (
              <SelectItem value="no-groups" disabled className="text-gray-400">
                No contact groups available
              </SelectItem>
            )}
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  // Step 2: Questions
  const renderQuestions = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-white mb-2">Questions</h2>
        <p className="text-gray-400">Add questions to your survey</p>
      </div>

      <div className="space-y-4">
        {formData.questions.map((question) => (
          <Card key={question.id} className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-white font-medium">Question</span>
                  <Badge variant="outline" className="text-gray-300">
                    {question.type.replace('-', ' ')}
                  </Badge>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deleteQuestion(question.id)}
                  className="text-red-400 hover:text-red-300"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <Input
                  placeholder="Type your question here"
                  value={question.question}
                  onChange={(e) => updateQuestion(question.id, { question: e.target.value })}
                  className="bg-slate-700 border-slate-600 text-white"
                />

                <div>
                  <Label className="text-white">Question Type</Label>
                  <Select
                    value={question.type}
                    onValueChange={(value) => updateQuestion(question.id, { type: value as Question['type'] })}
                  >
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-800 border-slate-700">
                      <SelectItem value="linear-scale-agree" className="text-white">Linear Scale Agree</SelectItem>
                      <SelectItem value="multiple-choice" className="text-white">Multiple Choice</SelectItem>
                      <SelectItem value="text" className="text-white">Text</SelectItem>
                      <SelectItem value="rating" className="text-white">Rating</SelectItem>
                      <SelectItem value="yes-no" className="text-white">Yes/No</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        <Button
          onClick={addQuestion}
          className="w-full bg-red-600 hover:bg-red-700 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New
        </Button>
      </div>
    </div>
  );

  // Step 3: Settings
  const renderSettings = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-white mb-2">Survey Settings</h2>
        <p className="text-gray-400">Enter these settings</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <Switch
              checked={formData.autoReminder}
              onCheckedChange={(checked) => updateFormData('autoReminder', checked)}
            />
            <Label className="text-white">Auto Reminder</Label>
          </div>

          <div>
            <Label className="text-white">Optin Message</Label>
            <Textarea
              placeholder="Enter Optin Message"
              value={formData.optinMessage}
              onChange={(e) => updateFormData('optinMessage', e.target.value)}
              className="bg-slate-700 border-slate-600 text-white"
              rows={4}
            />
          </div>

          <div className="flex items-center space-x-3">
            <Switch
              checked={formData.savePoint}
              onCheckedChange={(checked) => updateFormData('savePoint', checked)}
            />
            <Label className="text-white">Save Point</Label>
          </div>

          <div className="flex items-center space-x-3">
            <Switch
              checked={formData.reward}
              onCheckedChange={(checked) => updateFormData('reward', checked)}
            />
            <Label className="text-white">Reward</Label>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          <div>
            <Label className="text-white">Optout Message</Label>
            <Textarea
              placeholder="Enter Optout Message"
              value={formData.optoutMessage}
              onChange={(e) => updateFormData('optoutMessage', e.target.value)}
              className="bg-slate-700 border-slate-600 text-white"
              rows={4}
            />
          </div>

          <div className="flex items-center space-x-3">
            <Switch
              checked={formData.whitelist}
              onCheckedChange={(checked) => updateFormData('whitelist', checked)}
            />
            <Label className="text-white">Whitelist</Label>
          </div>
        </div>
      </div>
    </div>
  );

  // Step 4: Confirm
  const renderConfirm = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-white mb-2">Survey Details</h2>
        <p className="text-gray-400">Review your survey before submitting</p>
      </div>

      <div className="bg-slate-800 border border-slate-700 rounded-lg p-6 space-y-6">
        {/* Survey Details Summary */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">Survey Details</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Survey Name:</span>
              <span className="text-white ml-2">{formData.surveyName || 'Not set'}</span>
            </div>
            <div>
              <span className="text-gray-400">Survey Type:</span>
              <span className="text-white ml-2">{formData.surveyType}</span>
            </div>
            <div>
              <span className="text-gray-400">Start Date:</span>
              <span className="text-white ml-2">{formData.startDate || 'Not set'}</span>
            </div>
            <div>
              <span className="text-gray-400">End Date:</span>
              <span className="text-white ml-2">{formData.endDate || 'Not set'}</span>
            </div>
            <div>
              <span className="text-gray-400">Channel:</span>
              <span className="text-white ml-2">{formData.channel || 'Not set'}</span>
            </div>
            <div>
              <span className="text-gray-400">Questions:</span>
              <span className="text-white ml-2">{formData.questions.length}</span>
            </div>
          </div>
        </div>

        {/* Survey Settings Summary */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">Survey Settings</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Auto Reminder:</span>
              <span className="text-white ml-2">{formData.autoReminder ? 'Enabled' : 'Disabled'}</span>
            </div>
            <div>
              <span className="text-gray-400">Save Point:</span>
              <span className="text-white ml-2">{formData.savePoint ? 'Enabled' : 'Disabled'}</span>
            </div>
            <div>
              <span className="text-gray-400">Whitelist:</span>
              <span className="text-white ml-2">{formData.whitelist ? 'Enabled' : 'Disabled'}</span>
            </div>
            <div>
              <span className="text-gray-400">Reward:</span>
              <span className="text-white ml-2">{formData.reward ? 'Enabled' : 'Disabled'}</span>
            </div>
          </div>
        </div>

        {/* Questions Summary */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">Questions</h3>
          <div className="space-y-2">
            {formData.questions.map((question, index) => (
              <div key={question.id} className="bg-slate-700 p-3 rounded">
                <div className="flex items-center justify-between">
                  <span className="text-white">Question {index + 1}</span>
                  <Badge variant="outline" className="text-gray-300">
                    {question.type.replace('-', ' ')}
                  </Badge>
                </div>
                <p className="text-gray-300 text-sm mt-1">
                  {question.question || 'No question text'}
                </p>
              </div>
            ))}
            {formData.questions.length === 0 && (
              <p className="text-gray-400 text-sm">No questions added</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-slate-900 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Create Survey</h1>
        </div>
      </div>

      {/* Step Navigation */}
      <div className="flex items-center justify-center mb-8">
        <div className="flex items-center space-x-4">
          {steps.map((step, index) => {
            const stepNumber = index + 1;
            const isActive = step === currentStep;
            const isCompleted = steps.indexOf(currentStep) > index;

            return (
              <div key={step} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full cursor-pointer transition-colors ${
                    isActive
                      ? 'bg-blue-600 text-white'
                      : isCompleted
                      ? 'bg-green-600 text-white'
                      : 'bg-slate-700 text-gray-400'
                  }`}
                  onClick={() => goToStep(step)}
                >
                  {isCompleted ? <Check className="h-5 w-5" /> : stepNumber}
                </div>
                <span
                  className={`ml-2 text-sm font-medium capitalize cursor-pointer ${
                    isActive ? 'text-blue-400' : isCompleted ? 'text-green-400' : 'text-gray-400'
                  }`}
                  onClick={() => goToStep(step)}
                >
                  {step === 'details' ? 'Survey Details' : step}
                </span>
                {index < steps.length - 1 && (
                  <div className="w-8 h-px bg-slate-600 mx-4" />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <div className="max-w-4xl mx-auto">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-8">
            {currentStep === 'details' && renderSurveyDetails()}
            {currentStep === 'questions' && renderQuestions()}
            {currentStep === 'settings' && renderSettings()}
            {currentStep === 'confirm' && renderConfirm()}
          </CardContent>
        </Card>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center mt-8 max-w-4xl mx-auto">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStepIndex === 0}
          className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex space-x-4">
          {currentStep === 'confirm' ? (
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {loading ? 'Submitting...' : 'Submit'}
            </Button>
          ) : (
            <Button
              onClick={nextStep}
              disabled={currentStepIndex === steps.length - 1}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateSurvey;
