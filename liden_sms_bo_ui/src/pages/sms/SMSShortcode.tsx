import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  Hash, 
  Send, 
  MessageCircle, 
  TrendingUp,
  Users,
  Activity,
  Filter,
  Plus
} from "lucide-react";

const SMSShortcode = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);
  const [keyword, setKeyword] = useState("");
  const [response, setResponse] = useState("");
  const [shortcode, setShortcode] = useState("");

  // Mock data for shortcode campaigns
  const shortcodeCampaigns = [
    {
      id: "1",
      shortcode: "22100",
      keyword: "VOTE",
      response: "Thank you for voting! Your vote has been recorded.",
      received: 5420,
      responded: 5380,
      status: "Active",
      date: "2024-01-15",
    },
    {
      id: "2",
      shortcode: "22100",
      keyword: "INFO",
      response: "For more information, visit our website or call customer service.",
      received: 2150,
      responded: 2140,
      status: "Active",
      date: "2024-01-14",
    },
    {
      id: "3",
      shortcode: "22200",
      keyword: "PROMO",
      response: "Get 50% off your next purchase! Use code SAVE50 at checkout.",
      received: 8900,
      responded: 8850,
      status: "Paused",
      date: "2024-01-13",
    },
  ];

  const columns = [
    {
      accessorKey: "shortcode",
      header: "Shortcode",
      cell: ({ row }: any) => (
        <Badge variant="outline" className="font-mono text-lg">
          {row.getValue("shortcode")}
        </Badge>
      ),
    },
    {
      accessorKey: "keyword",
      header: "Keyword",
      cell: ({ row }: any) => (
        <Badge variant="secondary" className="font-mono">
          {row.getValue("keyword")}
        </Badge>
      ),
    },
    {
      accessorKey: "response",
      header: "Auto Response",
      cell: ({ row }: any) => (
        <div className="max-w-xs truncate">
          {row.getValue("response")}
        </div>
      ),
    },
    {
      accessorKey: "received",
      header: "Received",
      cell: ({ row }: any) => (
        <span className="font-medium">{row.getValue("received").toLocaleString()}</span>
      ),
    },
    {
      accessorKey: "responded",
      header: "Responded",
      cell: ({ row }: any) => (
        <span className="font-medium text-green-600">{row.getValue("responded").toLocaleString()}</span>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        return (
          <Badge 
            variant={status === "Active" ? "default" : status === "Paused" ? "secondary" : "destructive"}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "date",
      header: "Created",
    },
  ];

  const handleCreateKeyword = () => {
    // Handle keyword creation logic
    console.log("Creating keyword:", { shortcode, keyword, response });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Hash className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">SMS Shortcode</h1>
            <p className="text-muted-foreground">Manage shortcode campaigns and keywords</p>
          </div>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700 text-white">
          <Plus className="mr-2 h-4 w-4" />
          Request Shortcode
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Hash className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Shortcodes</p>
                <p className="text-2xl font-bold">3</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Messages Received</p>
                <p className="text-2xl font-bold">16,470</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Send className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Auto Responses</p>
                <p className="text-2xl font-bold">16,370</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Response Rate</p>
                <p className="text-2xl font-bold">99.4%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="keywords" className="space-y-4">
        <TabsList>
          <TabsTrigger value="keywords">Keywords</TabsTrigger>
          <TabsTrigger value="create">Create Keyword</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="keywords" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Shortcode Keywords</CardTitle>
                  <CardDescription>Manage your shortcode keywords and auto-responses</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={shortcodeCampaigns} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Create New Keyword</CardTitle>
              <CardDescription>
                Set up a new keyword with auto-response for your shortcode
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shortcode">Shortcode</Label>
                  <Select value={shortcode} onValueChange={setShortcode}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select shortcode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="22100">22100</SelectItem>
                      <SelectItem value="22200">22200</SelectItem>
                      <SelectItem value="22300">22300</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="keyword">Keyword</Label>
                  <Input
                    id="keyword"
                    placeholder="Enter keyword (e.g., VOTE, INFO)"
                    value={keyword}
                    onChange={(e) => setKeyword(e.target.value.toUpperCase())}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="response">Auto Response Message</Label>
                <Textarea
                  id="response"
                  placeholder="Enter the automatic response message..."
                  value={response}
                  onChange={(e) => setResponse(e.target.value)}
                  rows={4}
                />
                <p className="text-sm text-muted-foreground">
                  {response.length}/160 characters
                </p>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Preview</h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  When users send "<span className="font-mono font-bold">{keyword || "KEYWORD"}</span>" to{" "}
                  <span className="font-mono font-bold">{shortcode || "SHORTCODE"}</span>, they will receive:
                </p>
                <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded border">
                  <p className="text-sm">{response || "Your auto-response message will appear here..."}</p>
                </div>
              </div>
              <Button onClick={handleCreateKeyword} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Create Keyword
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Top Keywords</CardTitle>
                <CardDescription>Most popular keywords this month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { keyword: "VOTE", count: 5420, percentage: 45 },
                    { keyword: "PROMO", count: 8900, percentage: 35 },
                    { keyword: "INFO", count: 2150, percentage: 20 },
                  ].map((item) => (
                    <div key={item.keyword} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="font-mono">
                          {item.keyword}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {item.count.toLocaleString()} messages
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${item.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{item.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Response Times</CardTitle>
                <CardDescription>Average response time by shortcode</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { shortcode: "22100", time: "0.5s", status: "Excellent" },
                    { shortcode: "22200", time: "0.8s", status: "Good" },
                    { shortcode: "22300", time: "1.2s", status: "Average" },
                  ].map((item) => (
                    <div key={item.shortcode} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="font-mono">
                          {item.shortcode}
                        </Badge>
                        <span className="text-sm font-medium">{item.time}</span>
                      </div>
                      <Badge 
                        variant={item.status === "Excellent" ? "default" : item.status === "Good" ? "secondary" : "outline"}
                      >
                        {item.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SMSShortcode;
