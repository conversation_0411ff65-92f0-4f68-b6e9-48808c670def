import { useState, useEffect } from "react"
import { Search, RefreshCw, Network, Globe } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { LoadingSpinner } from "@/components/LoadingSpinner"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { useDateRangePickerWithQuickSelect } from "@/hooks/useDateRangePicker"
import { LidenAPI } from "@/lib/api-index"

interface SMSNetwork {
  id: string
  name: string
  code: string
  status: string
  country: string
  created_at: string
  updated_at: string
}

interface SMSNetworksResponse {
  total_count: string
  data: SMSNetwork[]
}

export default function SMSNetworks() {
  const [networks, setNetworks] = useState<SMSNetwork[]>([])
  const [loading, setLoading] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [countryFilter, setCountryFilter] = useState<string>("all")

  // Date picker hook
  const datePicker = useDateRangePickerWithQuickSelect({
    onDateRangeChange: () => {
      setCurrentPage(1) // Reset to first page when date range changes
    }
  })

  const fetchNetworks = async () => {
    try {
      setLoading(true)
      const dateParams = datePicker.getAPIDateParams()
      const params = {
        limit: pageSize,
        offset: (currentPage - 1) * pageSize,
        status: statusFilter && statusFilter !== "all" ? statusFilter : undefined,
        country: countryFilter && countryFilter !== "all" ? countryFilter : undefined,
        ...dateParams, // Include start and end date parameters
      }

      const response = await LidenAPI.smsNetworks.getNetworks(params)
      
      if (response.code === "Success" && response.data?.data) {
        const networkData = response.data.data as SMSNetworksResponse
        setNetworks(networkData.data || [])
        setTotalCount(parseInt(networkData.total_count || "0"))
      }
    } catch (error) {
      console.error("Failed to fetch SMS networks:", error)
      setNetworks([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchNetworks()
  }, [currentPage, pageSize, statusFilter, countryFilter, datePicker.dateRange])

  const handleSearch = () => {
    setCurrentPage(1)
    fetchNetworks()
  }

  const handleRefresh = () => {
    fetchNetworks()
  }

  const getStatusBadge = (status: string) => {
    const statusColors: Record<string, string> = {
      'Active': 'bg-green-600',
      'Inactive': 'bg-red-600',
      'Pending': 'bg-yellow-600'
    }
    
    return (
      <Badge 
        variant="outline" 
        className={`${statusColors[status] || 'bg-gray-600'} text-white border-0`}
      >
        {status}
      </Badge>
    )
  }

  const getCountryBadge = (country: string) => {
    const countryColors: Record<string, string> = {
      'Kenya': 'bg-green-600',
      'Uganda': 'bg-blue-600',
      'Tanzania': 'bg-purple-600'
    }
    
    return (
      <Badge 
        variant="outline" 
        className={`${countryColors[country] || 'bg-gray-600'} text-white border-0`}
      >
        {country}
      </Badge>
    )
  }

  const totalPages = Math.ceil(totalCount / pageSize)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>SMS</span>
          <span>›</span>
          <span className="text-foreground">Networks</span>
        </div>
      </div>

      {/* Filters Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            SMS Networks Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Date Range Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Date Range</label>
            <DatePickerWithRange
              date={datePicker.dateRange}
              onDateChange={datePicker.setDateRange}
              placeholder="Select date range"
              className="w-full"
            />
          </div>

          {/* Other Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Per page</label>
              <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Country</label>
              <Select value={countryFilter} onValueChange={setCountryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Countries" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Countries</SelectItem>
                  <SelectItem value="Kenya">Kenya</SelectItem>
                  <SelectItem value="Uganda">Uganda</SelectItem>
                  <SelectItem value="Tanzania">Tanzania</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search networks..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
                <Button onClick={handleSearch} variant="default">
                  Search
                </Button>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end">
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Networks Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              SMS Networks ({totalCount})
            </div>
            {datePicker.formattedRange && (
              <Badge variant="outline" className="text-sm">
                {datePicker.formattedRange}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Network Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Country</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Updated</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {networks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No networks found
                      </TableCell>
                    </TableRow>
                  ) : (
                    networks.map((network) => (
                      <TableRow key={network.id}>
                        <TableCell className="font-medium">{network.name}</TableCell>
                        <TableCell>{network.code}</TableCell>
                        <TableCell>{getCountryBadge(network.country)}</TableCell>
                        <TableCell>{getStatusBadge(network.status)}</TableCell>
                        <TableCell>{new Date(network.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>{new Date(network.updated_at).toLocaleDateString()}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-4">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                      
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1
                        return (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      })}
                      
                      <PaginationItem>
                        <PaginationNext 
                          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
