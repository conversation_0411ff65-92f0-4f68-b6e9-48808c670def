import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  Crown, 
  Send, 
  Clock, 
  CheckCircle, 
  XCircle, 
  MessageSquare,
  Users,
  TrendingUp,
  Filter
} from "lucide-react";

const SMSPremium = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);
  const [message, setMessage] = useState("");
  const [recipients, setRecipients] = useState("");
  const [senderId, setSenderId] = useState("");

  // Mock data for premium SMS campaigns
  const premiumCampaigns = [
    {
      id: "1",
      campaign: "Black Friday Sale",
      senderId: "SHOPNOW",
      recipients: 15000,
      sent: 14850,
      delivered: 14720,
      failed: 130,
      status: "Completed",
      cost: "$445.50",
      date: "2024-01-15",
    },
    {
      id: "2",
      campaign: "New Product Launch",
      senderId: "TECHCO",
      recipients: 8500,
      sent: 8500,
      delivered: 8350,
      failed: 150,
      status: "Completed",
      cost: "$255.00",
      date: "2024-01-14",
    },
    {
      id: "3",
      campaign: "Holiday Greetings",
      senderId: "COMPANY",
      recipients: 25000,
      sent: 24800,
      delivered: 0,
      failed: 0,
      status: "Sending",
      cost: "$750.00",
      date: "2024-01-16",
    },
  ];

  const columns = [
    {
      accessorKey: "campaign",
      header: "Campaign Name",
    },
    {
      accessorKey: "senderId",
      header: "Sender ID",
      cell: ({ row }: any) => (
        <Badge variant="outline" className="font-mono">
          {row.getValue("senderId")}
        </Badge>
      ),
    },
    {
      accessorKey: "recipients",
      header: "Recipients",
      cell: ({ row }: any) => (
        <span className="font-medium">{row.getValue("recipients").toLocaleString()}</span>
      ),
    },
    {
      accessorKey: "delivered",
      header: "Delivered",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span>{row.getValue("delivered").toLocaleString()}</span>
        </div>
      ),
    },
    {
      accessorKey: "failed",
      header: "Failed",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <XCircle className="h-4 w-4 text-red-500" />
          <span>{row.getValue("failed").toLocaleString()}</span>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        return (
          <Badge 
            variant={status === "Completed" ? "default" : status === "Sending" ? "secondary" : "destructive"}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "cost",
      header: "Cost",
      cell: ({ row }: any) => (
        <span className="font-medium text-green-600">{row.getValue("cost")}</span>
      ),
    },
    {
      accessorKey: "date",
      header: "Date",
    },
  ];

  const handleSendPremium = () => {
    // Handle premium SMS sending logic
    console.log("Sending premium SMS:", { message, recipients, senderId });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <Crown className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Premium SMS</h1>
            <p className="text-muted-foreground">Send premium SMS with custom sender IDs</p>
          </div>
        </div>
        <Button className="bg-yellow-600 hover:bg-yellow-700 text-white">
          <Crown className="mr-2 h-4 w-4" />
          Upgrade Plan
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sent</p>
                <p className="text-2xl font-bold">48,150</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Delivered</p>
                <p className="text-2xl font-bold">47,870</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Recipients</p>
                <p className="text-2xl font-bold">48,500</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">98.7%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="compose" className="space-y-4">
        <TabsList>
          <TabsTrigger value="compose">Compose Premium SMS</TabsTrigger>
          <TabsTrigger value="campaigns">Campaign History</TabsTrigger>
          <TabsTrigger value="sender-ids">Sender IDs</TabsTrigger>
        </TabsList>

        <TabsContent value="compose" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Send Premium SMS</CardTitle>
              <CardDescription>
                Send SMS with custom sender ID and premium delivery
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="senderId">Sender ID</Label>
                  <Select value={senderId} onValueChange={setSenderId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select sender ID" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="COMPANY">COMPANY</SelectItem>
                      <SelectItem value="SHOPNOW">SHOPNOW</SelectItem>
                      <SelectItem value="TECHCO">TECHCO</SelectItem>
                      <SelectItem value="ALERTS">ALERTS</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="recipients">Recipients</Label>
                  <Input
                    id="recipients"
                    placeholder="Enter phone numbers (comma separated)"
                    value={recipients}
                    onChange={(e) => setRecipients(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  placeholder="Enter your premium SMS message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={4}
                />
                <p className="text-sm text-muted-foreground">
                  {message.length}/160 characters
                </p>
              </div>
              <Button onClick={handleSendPremium} className="w-full">
                <Send className="mr-2 h-4 w-4" />
                Send Premium SMS
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Premium SMS Campaigns</CardTitle>
                  <CardDescription>View and manage your premium SMS campaigns</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={premiumCampaigns} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sender-ids" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sender IDs</CardTitle>
              <CardDescription>Manage your premium sender IDs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {["COMPANY", "SHOPNOW", "TECHCO", "ALERTS"].map((id) => (
                    <Card key={id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-mono font-medium">{id}</p>
                            <p className="text-sm text-muted-foreground">Active</p>
                          </div>
                          <Badge variant="outline" className="text-green-600">
                            Approved
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                <Button variant="outline">
                  Request New Sender ID
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SMSPremium;
