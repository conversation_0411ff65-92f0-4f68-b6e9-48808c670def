import { useState } from "react";
import { MessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import SMSCampaignTable from "@/components/SMSCampaignTable";
import ComposeMessagePopup from "@/components/ComposeMessagePopup";

export default function SMSBulkOutbox() {
  const [isComposeOpen, setIsComposeOpen] = useState(false);

  const handleComposeSend = (data: { from: string; to: string; message: string }) => {
    console.log("Sending SMS:", data);
    // TODO: Implement SMS sending logic here using the APIs
  };

  return (
    <div className="space-y-6 bg-slate-900 min-h-screen p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Outbox</h1>
          <p className="text-gray-400">Manage your SMS campaigns and messages</p>
        </div>
        <Button
          onClick={() => setIsComposeOpen(true)}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Compose Message
        </Button>
      </div>

      {/* SMS Campaign Table with Tabs */}
      <SMSCampaignTable />

      {/* Compose Message Popup */}
      <ComposeMessagePopup
        isOpen={isComposeOpen}
        onClose={() => setIsComposeOpen(false)}
        onSend={handleComposeSend}
      />
    </div>
  );
}