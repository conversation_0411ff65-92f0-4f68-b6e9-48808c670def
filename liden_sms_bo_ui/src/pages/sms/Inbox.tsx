import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  Inbox as InboxIcon, 
  Send, 
  MessageCircle, 
  Search,
  Filter,
  Reply,
  Archive,
  Trash2,
  Star,
  Clock,
  CheckCircle2
} from "lucide-react";

const Inbox = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  const [replyMessage, setReplyMessage] = useState("");

  // Mock data for inbox messages
  const inboxMessages = [
    {
      id: "1",
      from: "+************",
      message: "Hello, I need help with my account balance",
      timestamp: "2024-01-16 14:30",
      status: "Unread",
      type: "Support",
      starred: false,
    },
    {
      id: "2",
      from: "+************",
      message: "Thank you for the promotional offer. How can I redeem it?",
      timestamp: "2024-01-16 13:45",
      status: "Read",
      type: "Promotion",
      starred: true,
    },
    {
      id: "3",
      from: "+************",
      message: "STOP",
      timestamp: "2024-01-16 12:20",
      status: "Read",
      type: "Opt-out",
      starred: false,
    },
    {
      id: "4",
      from: "+************",
      message: "When is my next appointment scheduled?",
      timestamp: "2024-01-16 11:15",
      status: "Replied",
      type: "Inquiry",
      starred: false,
    },
    {
      id: "5",
      from: "+************",
      message: "I didn't receive my order confirmation SMS",
      timestamp: "2024-01-16 10:30",
      status: "Unread",
      type: "Support",
      starred: false,
    },
  ];

  const columns = [
    {
      accessorKey: "from",
      header: "From",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          {row.original.starred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
          <span className="font-mono">{row.getValue("from")}</span>
        </div>
      ),
    },
    {
      accessorKey: "message",
      header: "Message",
      cell: ({ row }: any) => (
        <div className="max-w-md truncate">
          {row.getValue("message")}
        </div>
      ),
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }: any) => {
        const type = row.getValue("type");
        const colors = {
          Support: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          Promotion: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          "Opt-out": "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          Inquiry: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400",
        };
        return (
          <Badge variant="secondary" className={colors[type as keyof typeof colors]}>
            {type}
          </Badge>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        const icons = {
          Unread: <MessageCircle className="h-4 w-4 text-blue-500" />,
          Read: <CheckCircle2 className="h-4 w-4 text-gray-500" />,
          Replied: <Reply className="h-4 w-4 text-green-500" />,
        };
        return (
          <div className="flex items-center space-x-2">
            {icons[status as keyof typeof icons]}
            <span>{status}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "timestamp",
      header: "Received",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{row.getValue("timestamp")}</span>
        </div>
      ),
    },
  ];

  const handleReply = () => {
    if (selectedMessage && replyMessage.trim()) {
      console.log("Replying to:", selectedMessage.from, "Message:", replyMessage);
      setReplyMessage("");
      setSelectedMessage(null);
    }
  };

  const handleMarkAsRead = (messageId: string) => {
    console.log("Marking as read:", messageId);
  };

  const handleArchive = (messageId: string) => {
    console.log("Archiving:", messageId);
  };

  const handleDelete = (messageId: string) => {
    console.log("Deleting:", messageId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <InboxIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Inbox</h1>
            <p className="text-muted-foreground">Manage incoming SMS messages and replies</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Archive className="mr-2 h-4 w-4" />
            Archive All Read
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageCircle className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Messages</p>
                <p className="text-2xl font-bold">1,247</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageCircle className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Unread</p>
                <p className="text-2xl font-bold">23</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Reply className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Replied</p>
                <p className="text-2xl font-bold">892</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Response Time</p>
                <p className="text-2xl font-bold">2.5h</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Messages</TabsTrigger>
          <TabsTrigger value="unread">Unread (23)</TabsTrigger>
          <TabsTrigger value="support">Support</TabsTrigger>
          <TabsTrigger value="optouts">Opt-outs</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>All Messages</CardTitle>
                  <CardDescription>View and manage all incoming SMS messages</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search messages..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable 
                columns={columns} 
                data={inboxMessages}
                onRowClick={(row) => setSelectedMessage(row)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="unread" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Unread Messages</CardTitle>
              <CardDescription>Messages that require your attention</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable 
                columns={columns} 
                data={inboxMessages.filter(msg => msg.status === "Unread")}
                onRowClick={(row) => setSelectedMessage(row)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="support" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Support Messages</CardTitle>
              <CardDescription>Customer support and inquiry messages</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable 
                columns={columns} 
                data={inboxMessages.filter(msg => msg.type === "Support" || msg.type === "Inquiry")}
                onRowClick={(row) => setSelectedMessage(row)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optouts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Opt-out Requests</CardTitle>
              <CardDescription>Users who have requested to unsubscribe</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable 
                columns={columns} 
                data={inboxMessages.filter(msg => msg.type === "Opt-out")}
                onRowClick={(row) => setSelectedMessage(row)}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Reply Modal/Panel */}
      {selectedMessage && (
        <Card className="fixed bottom-4 right-4 w-96 shadow-lg border-2">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Reply to {selectedMessage.from}</CardTitle>
              <Button variant="ghost" size="sm" onClick={() => setSelectedMessage(null)}>
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-muted p-3 rounded">
              <p className="text-sm">{selectedMessage.message}</p>
              <p className="text-xs text-muted-foreground mt-1">{selectedMessage.timestamp}</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="reply">Your Reply</Label>
              <Textarea
                id="reply"
                placeholder="Type your reply..."
                value={replyMessage}
                onChange={(e) => setReplyMessage(e.target.value)}
                rows={3}
              />
              <p className="text-sm text-muted-foreground">
                {replyMessage.length}/160 characters
              </p>
            </div>
            <div className="flex space-x-2">
              <Button onClick={handleReply} className="flex-1">
                <Send className="mr-2 h-4 w-4" />
                Send Reply
              </Button>
              <Button variant="outline" onClick={() => handleMarkAsRead(selectedMessage.id)}>
                Mark Read
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Inbox;
