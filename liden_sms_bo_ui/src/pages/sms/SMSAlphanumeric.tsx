import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  Type, 
  Send, 
  CheckCircle, 
  XCircle, 
  MessageSquare,
  Users,
  TrendingUp,
  Filter,
  Plus,
  AlertCircle
} from "lucide-react";

const SMSAlphanumeric = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);
  const [message, setMessage] = useState("");
  const [recipients, setRecipients] = useState("");
  const [senderId, setSenderId] = useState("");

  // Mock data for alphanumeric SMS campaigns
  const alphanumericCampaigns = [
    {
      id: "1",
      campaign: "Account Alert",
      senderId: "MyBank",
      recipients: 12000,
      sent: 12000,
      delivered: 11850,
      failed: 150,
      status: "Completed",
      cost: "$180.00",
      date: "2024-01-15",
    },
    {
      id: "2",
      campaign: "Order Confirmation",
      senderId: "ShopEasy",
      recipients: 8500,
      sent: 8500,
      delivered: 8420,
      failed: 80,
      status: "Completed",
      cost: "$127.50",
      date: "2024-01-14",
    },
    {
      id: "3",
      campaign: "Appointment Reminder",
      senderId: "HealthCare",
      recipients: 3200,
      sent: 3200,
      delivered: 0,
      failed: 0,
      status: "Sending",
      cost: "$48.00",
      date: "2024-01-16",
    },
  ];

  const columns = [
    {
      accessorKey: "campaign",
      header: "Campaign Name",
    },
    {
      accessorKey: "senderId",
      header: "Sender ID",
      cell: ({ row }: any) => (
        <Badge variant="outline" className="font-mono">
          {row.getValue("senderId")}
        </Badge>
      ),
    },
    {
      accessorKey: "recipients",
      header: "Recipients",
      cell: ({ row }: any) => (
        <span className="font-medium">{row.getValue("recipients").toLocaleString()}</span>
      ),
    },
    {
      accessorKey: "delivered",
      header: "Delivered",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span>{row.getValue("delivered").toLocaleString()}</span>
        </div>
      ),
    },
    {
      accessorKey: "failed",
      header: "Failed",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <XCircle className="h-4 w-4 text-red-500" />
          <span>{row.getValue("failed").toLocaleString()}</span>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        return (
          <Badge 
            variant={status === "Completed" ? "default" : status === "Sending" ? "secondary" : "destructive"}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "cost",
      header: "Cost",
      cell: ({ row }: any) => (
        <span className="font-medium text-green-600">{row.getValue("cost")}</span>
      ),
    },
    {
      accessorKey: "date",
      header: "Date",
    },
  ];

  const handleSendAlphanumeric = () => {
    // Handle alphanumeric SMS sending logic
    console.log("Sending alphanumeric SMS:", { message, recipients, senderId });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Type className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Alphanumeric SMS</h1>
            <p className="text-muted-foreground">Send SMS with custom alphanumeric sender IDs</p>
          </div>
        </div>
        <Button className="bg-purple-600 hover:bg-purple-700 text-white">
          <Plus className="mr-2 h-4 w-4" />
          Request Sender ID
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sent</p>
                <p className="text-2xl font-bold">23,700</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Delivered</p>
                <p className="text-2xl font-bold">23,270</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Recipients</p>
                <p className="text-2xl font-bold">23,700</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">98.2%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="compose" className="space-y-4">
        <TabsList>
          <TabsTrigger value="compose">Compose SMS</TabsTrigger>
          <TabsTrigger value="campaigns">Campaign History</TabsTrigger>
          <TabsTrigger value="sender-ids">Sender IDs</TabsTrigger>
        </TabsList>

        <TabsContent value="compose" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Send Alphanumeric SMS</CardTitle>
              <CardDescription>
                Send SMS with custom alphanumeric sender ID (up to 11 characters)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-900 dark:text-amber-100">Important Note</h4>
                    <p className="text-sm text-amber-800 dark:text-amber-200">
                      Alphanumeric sender IDs are one-way only. Recipients cannot reply to these messages.
                      Sender IDs must be approved before use and can take 1-3 business days for approval.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="senderId">Sender ID</Label>
                  <Select value={senderId} onValueChange={setSenderId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select sender ID" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MyBank">MyBank</SelectItem>
                      <SelectItem value="ShopEasy">ShopEasy</SelectItem>
                      <SelectItem value="HealthCare">HealthCare</SelectItem>
                      <SelectItem value="TechSupport">TechSupport</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="recipients">Recipients</Label>
                  <Input
                    id="recipients"
                    placeholder="Enter phone numbers (comma separated)"
                    value={recipients}
                    onChange={(e) => setRecipients(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  placeholder="Enter your alphanumeric SMS message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={4}
                />
                <p className="text-sm text-muted-foreground">
                  {message.length}/160 characters
                </p>
              </div>
              <Button onClick={handleSendAlphanumeric} className="w-full">
                <Send className="mr-2 h-4 w-4" />
                Send Alphanumeric SMS
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Alphanumeric SMS Campaigns</CardTitle>
                  <CardDescription>View and manage your alphanumeric SMS campaigns</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={alphanumericCampaigns} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sender-ids" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Alphanumeric Sender IDs</CardTitle>
              <CardDescription>Manage your alphanumeric sender IDs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { id: "MyBank", status: "Approved", type: "Financial" },
                    { id: "ShopEasy", status: "Approved", type: "E-commerce" },
                    { id: "HealthCare", status: "Approved", type: "Healthcare" },
                    { id: "TechSupport", status: "Pending", type: "Technology" },
                  ].map((sender) => (
                    <Card key={sender.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-mono font-medium">{sender.id}</p>
                          <Badge 
                            variant={sender.status === "Approved" ? "default" : "secondary"}
                            className={sender.status === "Approved" ? "text-green-600" : "text-yellow-600"}
                          >
                            {sender.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{sender.type}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                
                <div className="border-t pt-4">
                  <h4 className="font-medium mb-2">Request New Sender ID</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input placeholder="Sender ID (max 11 characters)" />
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="financial">Financial</SelectItem>
                        <SelectItem value="ecommerce">E-commerce</SelectItem>
                        <SelectItem value="healthcare">Healthcare</SelectItem>
                        <SelectItem value="technology">Technology</SelectItem>
                        <SelectItem value="education">Education</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button className="mt-4" variant="outline">
                    <Plus className="mr-2 h-4 w-4" />
                    Submit Request
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SMSAlphanumeric;
