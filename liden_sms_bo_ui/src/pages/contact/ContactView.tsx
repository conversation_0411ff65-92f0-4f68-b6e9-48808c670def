import { useState, useEffect } from "react"
import { Search, Plus, Edit, Upload, Download, Users, RefreshCw } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { LoadingSpinner } from "@/components/LoadingSpinner"
import { EditContactModal } from "@/components/EditContactModal"
import { LidenAPI } from "@/lib/api-index"

// Interface for contact group from API
interface ContactGroup {
  total_count: string
  list_id: string
  group_name: string
  description: string
  status: string
  can_upload: string
  custom1: string
  custom2: string
  custom3: string
  custom4: string
  custom5: string
  deleted_on: string | null
  created_at: string
  created_by: string
}

// Interface for contact from API
interface Contact {
  total_count: string
  id: string
  list_id: string
  profile_id: string
  msisdn: string
  status: string
  custom1: string | null
  custom2: string | null
  custom3: string | null
  custom4: string | null
  custom5: string | null
  created_at: string
}

export function ContactView() {
  const [groups, setGroups] = useState<ContactGroup[]>([])
  const [contacts, setContacts] = useState<Contact[]>([])
  const [selectedGroup, setSelectedGroup] = useState<ContactGroup | null>(null)
  const [loading, setLoading] = useState(false)
  const [contactsLoading, setContactsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [contactSearchTerm, setContactSearchTerm] = useState("")
  const [totalCount, setTotalCount] = useState(0)
  const [contactsTotalCount, setContactsTotalCount] = useState(0)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editingContact, setEditingContact] = useState<any>(null)

  // Fetch contact groups from API
  const fetchGroups = async () => {
    try {
      setLoading(true)
      const params = {
        limit: 100,
        listName: searchTerm || "",
      }

      const response = await LidenAPI.contact.getGroups(params)
      
      if (response.status === 200) {
        setGroups(response.data || [])
        setTotalCount(parseInt(response.total_count || "0"))

        fetchContacts(response.data[0].list_id)
      }
    } catch (error) {
      console.error("Failed to fetch contact groups:", error)
      setGroups([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  // Fetch contacts for selected group
  const fetchContacts = async (listId: string) => {
    try {
      setContactsLoading(true)
      const params = {
        listId: listId,
        limit: 20,
      }

      const response = await LidenAPI.contact.getContacts(params)
      
      if (response.status === 200) {
        setContacts(response.data || [])
        setContactsTotalCount(parseInt(response.total_count || "0"))
      }
    } catch (error) {
      console.error("Failed to fetch contacts:", error)
      setContacts([])
      setContactsTotalCount(0)
    } finally {
      setContactsLoading(false)
    }
  }

  // Handle group selection
  const handleGroupSelect = (group: ContactGroup) => {
    setSelectedGroup(group)
    fetchContacts(group.list_id)
  }

  // Handle edit contact
  const handleEditContact = (contact: any) => {
    setEditingContact({
      group_name: selectedGroup?.group_name || "",
      description: selectedGroup?.description || "",
      custom_fields: [
        { name: "Name", value: contact.custom1 || "" },
        { name: "cs", value: contact.custom2 || "" },
        { name: "cs2", value: contact.custom3 || "" },
        { name: "cs3", value: contact.custom4 || "" },
        { name: "name", value: contact.custom5 || "" },
      ]
    })
    setEditModalOpen(true)
  }

  // Handle update contact
  const handleUpdateContact = (data: any) => {
    console.log("Updating contact:", data)
    // Here you would call the API to update the contact
    // For now, just close the modal
    setEditModalOpen(false)
    setEditingContact(null)
  }
  // useEffect hooks
  useEffect(() => {
    fetchGroups()
  }, [searchTerm])

  // Auto-select first group when groups are loaded
  useEffect(() => {
    if (groups.length > 0 && !selectedGroup) {
      const firstGroup = groups[0]
      setSelectedGroup(firstGroup)
      fetchContacts(firstGroup.list_id)
    }
  }, [groups, selectedGroup])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-white">View Contacts</h1>
        <Button className="bg-red-600 hover:bg-red-700">
          <Plus className="mr-2 h-4 w-4" />
          Add Contact
        </Button>
      </div>

      {/* Main Container */}
      <Card className="bg-slate-800 border-slate-700 h-[calc(100vh-200px)]">
        <CardContent className="p-0 h-full">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-0 h-full">
            {/* Left Sidebar - Contact Groups */}
            <div className="lg:col-span-1 border-r border-slate-700">
              <div className="p-4 border-b border-slate-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-white font-medium">Groups</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={fetchGroups}
                    className="text-slate-400 hover:text-white"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
                {/* Search Groups */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <Input
                    placeholder="Search Group"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>

              {/* Groups List */}
              <div className="p-4 space-y-2 h-[calc(100vh-350px)] overflow-y-auto">
                {loading ? (
                  <div className="flex justify-center py-4">
                    <LoadingSpinner />
                  </div>
                ) : groups.length === 0 ? (
                  <div className="text-center py-4 text-slate-400">
                    No groups found
                  </div>
                ) : (
                  groups.map((group) => (
                    <div
                      key={group.list_id}
                      onClick={() => handleGroupSelect(group)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedGroup?.list_id === group.list_id
                          ? 'bg-red-600 text-white'
                          : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                      }`}
                    >
                      <div className="font-medium">{group.group_name}</div>
                      <div className="text-sm opacity-75">
                        {group.total_count} contacts
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Right Content - Contact Details */}
            <div className="lg:col-span-3 flex flex-col h-full">
              {selectedGroup ? (
                <div className="flex flex-col h-full">
                  {/* Header */}
                  <div className="p-4 border-b border-slate-700">
                    <div className="flex justify-between items-center">
                      <h3 className="text-white font-medium">
                        {selectedGroup.group_name} Contacts
                      </h3>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Upload
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Download className="mr-2 h-4 w-4" />
                          Export
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 p-4 overflow-hidden">
                    {/* Contact Search */}
                    <div className="mb-4">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                        <Input
                          placeholder="Search Contacts"
                          value={contactSearchTerm}
                          onChange={(e) => setContactSearchTerm(e.target.value)}
                          className="pl-10 bg-slate-700 border-slate-600 text-white"
                        />
                      </div>
                    </div>

                    {/* Contacts Table */}
                    <div className="h-[calc(100vh-450px)] overflow-auto">
                      {contactsLoading ? (
                        <div className="flex justify-center py-8">
                          <LoadingSpinner />
                        </div>
                      ) : (
                        <>
                          <div className="text-sm text-slate-400 mb-4">
                            Total Contacts for {selectedGroup.group_name}: {contactsTotalCount}
                          </div>
                          <div className="rounded-md border border-slate-700">
                            <Table>
                              <TableHeader>
                                <TableRow className="border-slate-700">
                                  <TableHead className="text-slate-300">Phone</TableHead>
                                  <TableHead className="text-slate-300">custom1</TableHead>
                                  <TableHead className="text-slate-300">custom2</TableHead>
                                  <TableHead className="text-slate-300">custom3</TableHead>
                                  <TableHead className="text-slate-300">custom4</TableHead>
                                  <TableHead className="text-slate-300">status</TableHead>
                                  <TableHead className="text-slate-300">Action</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {contacts.length === 0 ? (
                                  <TableRow>
                                    <TableCell colSpan={7} className="text-center py-8 text-slate-400">
                                      No contacts found
                                    </TableCell>
                                  </TableRow>
                                ) : (
                                  contacts.map((contact) => (
                                    <TableRow key={contact.id} className="border-slate-700 hover:bg-slate-700/50">
                                      <TableCell className="text-white font-medium">
                                        {contact.msisdn}
                                      </TableCell>
                                      <TableCell className="text-slate-300">
                                        {contact.custom1 || "-"}
                                      </TableCell>
                                      <TableCell className="text-slate-300">
                                        {contact.custom2 || "-"}
                                      </TableCell>
                                      <TableCell className="text-slate-300">
                                        {contact.custom3 || "-"}
                                      </TableCell>
                                      <TableCell className="text-slate-300">
                                        {contact.custom4 || "-"}
                                      </TableCell>
                                      <TableCell>
                                        <Badge
                                          variant="default"
                                          className={contact.status === "1" ? "bg-green-600" : "bg-gray-600"}
                                        >
                                          {contact.status === "1" ? "Active" : "Inactive"}
                                        </Badge>
                                      </TableCell>
                                      <TableCell>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleEditContact(contact)}
                                          className="text-slate-400 hover:text-white"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </TableCell>
                                    </TableRow>
                                  ))
                                )}
                              </TableBody>
                            </Table>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <Users className="mx-auto h-12 w-12 text-slate-400 mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">
                      Select a Contact Group
                    </h3>
                    <p className="text-slate-400">
                      Choose a contact group from the sidebar to view its contacts
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Edit Contact Modal */}
      <EditContactModal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        onUpdate={handleUpdateContact}
        contactData={editingContact}
      />
    </div>
  )
}

export default ContactView