import { useState, useEffect, useCallback } from "react"
import { Search, Filter, Download, Refresh<PERSON><PERSON>, Eye, Calendar } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/DateRangePicker"
import { DataTable, Column } from "@/components/DataTable"
import { LidenAPI } from "@/lib/api-index"
import { Invoice, InvoiceParams } from "@/lib/api-types"
import { LoadingSpinner } from "@/components/LoadingSpinner"

export default function InvoicePage() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedService, setSelectedService] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [selectedInvoiceState, setSelectedInvoiceState] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const pageSize = 20

  // Fetch invoices from API
  const fetchInvoices = useCallback(async () => {
    try {
      setLoading(true)
      const offset = ((currentPage - 1) * pageSize) + 1

      const params: InvoiceParams = {
        limit: pageSize,
        offset: offset,
      }

      // Only add parameters if they have values
      if (selectedService && selectedService !== "all") {
        params.service_id = selectedService
      }
      if (selectedStatus && selectedStatus !== "all") {
        params.status = selectedStatus
      }
      if (selectedInvoiceState && selectedInvoiceState !== "all") {
        params.invoice_state = selectedInvoiceState
      }

      const response = await LidenAPI.invoice.getInvoices(params)

      if (response.success && response.data) {
        const invoiceData = response.data.data || []
        setInvoices(invoiceData)
        setTotalCount(parseInt(response.data.total_count) || 0)
      } else {
        setInvoices([])
        setTotalCount(0)
      }
    } catch (error) {
      console.error("Failed to fetch invoices:", error)
      setInvoices([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }, [currentPage, selectedService, selectedStatus, selectedInvoiceState])

  useEffect(() => {
    fetchInvoices()
  }, [fetchInvoices])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleRefresh = () => {
    fetchInvoices()
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log("Export invoices")
  }

  const handleViewInvoice = (invoice: Invoice) => {
    // TODO: Implement view invoice details
    console.log("View invoice:", invoice.invoiceId)
  }

  // Filter invoices based on search query
  const filteredInvoices = invoices.filter(invoice =>
    invoice.reference_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
    invoice.service_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    invoice.client_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    invoice.client_email.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getStatusBadge = (status: string, statusDescription: string) => {
    const statusMap: { [key: string]: { variant: "default" | "secondary" | "destructive" | "outline", label: string } } = {
      "50": { variant: "secondary", label: "New" },
      "100": { variant: "outline", label: "Pending" },
      "200": { variant: "default", label: "Paid" },
      "300": { variant: "destructive", label: "Cancelled" },
    }
    
    const statusInfo = statusMap[status] || { variant: "outline", label: statusDescription }
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>
  }

  const getInvoiceStateBadge = (state: string) => {
    const stateMap: { [key: string]: { variant: "default" | "secondary" | "destructive" | "outline", color: string } } = {
      "Paid": { variant: "default", color: "text-green-600" },
      "OverDue": { variant: "destructive", color: "text-red-600" },
      "Pending": { variant: "secondary", color: "text-yellow-600" },
      "New": { variant: "outline", color: "text-blue-600" },
    }
    
    const stateInfo = stateMap[state] || { variant: "outline", color: "text-gray-600" }
    return <Badge variant={stateInfo.variant}>{state}</Badge>
  }

  const columns: Column<Invoice>[] = [
    {
      key: "reference_number",
      header: "Reference",
      render: (invoice) => (
        <div className="font-medium">
          {invoice.reference_number}
          <div className="text-sm text-muted-foreground">ID: {invoice.invoiceId}</div>
        </div>
      ),
    },
    {
      key: "service_name",
      header: "Service",
      render: (invoice) => (
        <div>
          <div className="font-medium">{invoice.service_name}</div>
          <div className="text-sm text-muted-foreground">ID: {invoice.service_id}</div>
        </div>
      ),
    },
    {
      key: "amount",
      header: "Amount",
      render: (invoice) => (
        <div className="text-right">
          <div className="font-medium">{invoice.currency} {parseFloat(invoice.amount).toLocaleString()}</div>
          {parseFloat(invoice.vat_tax_amount) > 0 && (
            <div className="text-sm text-muted-foreground">VAT: {invoice.currency} {parseFloat(invoice.vat_tax_amount).toLocaleString()}</div>
          )}
        </div>
      ),
    },
    {
      key: "client_name",
      header: "Client",
      render: (invoice) => (
        <div>
          <div className="font-medium">{invoice.client_name}</div>
          <div className="text-sm text-muted-foreground">{invoice.client_email}</div>
        </div>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (invoice) => getStatusBadge(invoice.status, invoice.status_description),
    },
    {
      key: "invoice_state",
      header: "State",
      render: (invoice) => getInvoiceStateBadge(invoice.invoice_state),
    },
    {
      key: "due_date",
      header: "Due Date",
      render: (invoice) => (
        <div className="text-sm">
          <div>{new Date(invoice.due_date).toLocaleDateString()}</div>
          <div className="text-muted-foreground">{new Date(invoice.due_date).toLocaleTimeString()}</div>
        </div>
      ),
    },
    {
      key: "created",
      header: "Created",
      render: (invoice) => (
        <div className="text-sm">
          <div>{new Date(invoice.created).toLocaleDateString()}</div>
          <div className="text-muted-foreground">by {invoice.full_names}</div>
        </div>
      ),
    },
    {
      key: "actions",
      header: "Actions",
      render: (invoice) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewInvoice(invoice)}
        >
          <Eye className="h-4 w-4" />
        </Button>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Invoices</h1>
          <p className="text-muted-foreground">
            Manage and track system invoices for services and wallet topups
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center gap-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search reference, service, client..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedService} onValueChange={setSelectedService}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Service" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Services</SelectItem>
              <SelectItem value="2">SenderId Management</SelectItem>
              <SelectItem value="16">Wallet Topup</SelectItem>
              <SelectItem value="1">Bulk SMS Service</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="50">New</SelectItem>
              <SelectItem value="100">Pending</SelectItem>
              <SelectItem value="200">Paid</SelectItem>
              <SelectItem value="300">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedInvoiceState} onValueChange={setSelectedInvoiceState}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="State" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All States</SelectItem>
              <SelectItem value="New">New</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="Paid">Paid</SelectItem>
              <SelectItem value="OverDue">OverDue</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Data Table */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : (
        <DataTable
          data={filteredInvoices}
          columns={columns}
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / pageSize)}
          onPageChange={handlePageChange}
          loading={loading}
        />
      )}
    </div>
  )
}
