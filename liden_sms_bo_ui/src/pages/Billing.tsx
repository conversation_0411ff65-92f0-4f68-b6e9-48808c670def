import { useState, useEffect, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { LidenAPI } from "@/lib/api-index";
import { AccountTransaction, AccountTransactionParams } from "@/lib/api-types";
import {
  CreditCard,
  Download,
  DollarSign,
  TrendingUp,
  Calendar,
  Receipt,
  Filter,
  Eye,
  FileText,
  Plus,
  Wallet,
  ArrowUpCircle,
  ArrowDownCircle,
  RefreshCw
} from "lucide-react";

const Billing = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const [selectedDateRange, setSelectedDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: new Date(new Date().setMonth(new Date().getMonth() - 3)), // Default to 3 months ago
    to: new Date(),
  });

  // State for transactions
  const [expenditures, setExpenditures] = useState<AccountTransaction[]>([]);
  const [topUps, setTopUps] = useState<AccountTransaction[]>([]);
  const [refunds, setRefunds] = useState<AccountTransaction[]>([]);
  const [loading, setLoading] = useState(false);

  // Determine active tab from URL
  const getActiveTabFromPath = () => {
    const path = location.pathname;
    if (path.includes('/payment-methods')) return 'payment-methods';
    if (path.includes('/top-ups')) return 'top-ups';
    if (path.includes('/expenditures')) return 'expenditures';
    if (path.includes('/refunds')) return 'refunds';
    return 'payment-methods'; // default
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromPath());

  // Handle tab change and update URL
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const basePath = '/billing';
    const newPath = value === 'payment-methods' ? basePath : `${basePath}/${value}`;
    navigate(newPath);
  };

  // Update active tab when URL changes
  useEffect(() => {
    setActiveTab(getActiveTabFromPath());
  }, [location.pathname]);

  // Top-up form state
  const [topUpAmount, setTopUpAmount] = useState("");
  const [topUpMethod, setTopUpMethod] = useState("");

  // Fetch transactions based on reference type
  const fetchTransactions = useCallback(async (referenceType: string) => {
    try {
      setLoading(true);
      const params: AccountTransactionParams = {
        start: selectedDateRange.from?.toISOString().split('T')[0] || '2025-06-01',
        end: selectedDateRange.to?.toISOString().split('T')[0] || '2025-09-23',
        referenceType: referenceType,
        limit: 50
      };

      const response = await LidenAPI.accountTransactions.getTransactions(params);

      if (response.success && response.data?.data?.data) {
        return response.data.data.data;
      }
      return [];
    } catch (error) {
      console.error(`Failed to fetch transactions for type ${referenceType}:`, error);
      return [];
    } finally {
      setLoading(false);
    }
  }, [selectedDateRange]);

  // Load all transaction types
  const loadAllTransactions = useCallback(async () => {
    setLoading(true);
    try {
      const [expendituresData, topUpsData, refundsData] = await Promise.all([
        fetchTransactions('1'), // Expenditures/Debits
        fetchTransactions('2'), // Top-ups/Credits
        fetchTransactions('3'), // Refunds (assuming type 3)
      ]);

      setExpenditures(expendituresData);
      setTopUps(topUpsData);
      setRefunds(refundsData);
    } catch (error) {
      console.error('Failed to load transactions:', error);
    } finally {
      setLoading(false);
    }
  }, [fetchTransactions]);

  useEffect(() => {
    loadAllTransactions();
  }, [loadAllTransactions]);

  // Handle top-up form submission
  const handleTopUpSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement top-up functionality
    console.log('Top-up submitted:', { amount: topUpAmount, method: topUpMethod });
  };

  // Transaction table columns
  const transactionColumns = [
    {
      accessorKey: "transacion_id",
      header: "Transaction ID",
      cell: ({ row }: any) => (
        <span className="font-mono text-sm">{row.getValue("transacion_id")}</span>
      ),
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }: any) => {
        const amount = parseFloat(row.getValue("amount"));
        const isNegative = amount < 0;
        return (
          <span className={`font-medium ${isNegative ? 'text-red-600' : 'text-green-600'}`}>
            {row.getValue("currency")} {Math.abs(amount).toFixed(2)}
          </span>
        );
      },
    },
    {
      accessorKey: "reference_type",
      header: "Type",
      cell: ({ row }: any) => (
        <Badge variant={row.getValue("reference_type") === "DEBIT" ? "destructive" : "default"}>
          {row.getValue("reference_type")}
        </Badge>
      ),
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }: any) => (
        <span className="text-sm max-w-xs truncate">{row.getValue("description")}</span>
      ),
    },
    {
      accessorKey: "source",
      header: "Source",
      cell: ({ row }: any) => (
        <span className="text-sm text-muted-foreground">{row.getValue("source")}</span>
      ),
    },
    {
      accessorKey: "created_at",
      header: "Date",
      cell: ({ row }: any) => (
        <span className="text-sm">{new Date(row.getValue("created_at")).toLocaleDateString()}</span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Wallet className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Billing & Account Management</h1>
            <p className="text-muted-foreground">Manage your account balance, transactions, and payment methods</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={loadAllTransactions} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <ArrowDownCircle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Expenditures</p>
                <p className="text-2xl font-bold">{expenditures.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <ArrowUpCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Top-ups</p>
                <p className="text-2xl font-bold">{topUps.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <RefreshCw className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Refunds</p>
                <p className="text-2xl font-bold">{refunds.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Date Range</p>
                <p className="text-sm font-bold">
                  {selectedDateRange.from?.toLocaleDateString()} - {selectedDateRange.to?.toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList>
          <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="top-ups">Top Ups</TabsTrigger>
          <TabsTrigger value="expenditures">Expenditures</TabsTrigger>
          <TabsTrigger value="refunds">Refunds</TabsTrigger>
        </TabsList>

        {/* Payment Methods Tab */}
        <TabsContent value="payment-methods" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Account Top Up</CardTitle>
                <CardDescription>Add funds to your account balance</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTopUpSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (KES)</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={topUpAmount}
                      onChange={(e) => setTopUpAmount(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="method">Payment Method</Label>
                    <Select value={topUpMethod} onValueChange={setTopUpMethod}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mpesa">M-Pesa</SelectItem>
                        <SelectItem value="card">Credit/Debit Card</SelectItem>
                        <SelectItem value="bank">Bank Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button type="submit" className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Top Up Now
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>Manage your payment methods</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <CreditCard className="h-8 w-8 text-blue-500" />
                        <div>
                          <p className="font-medium">M-Pesa</p>
                          <p className="text-sm text-muted-foreground">Mobile Money</p>
                        </div>
                      </div>
                      <Badge variant="outline">Primary</Badge>
                    </div>
                  </div>
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <CreditCard className="h-8 w-8 text-green-500" />
                        <div>
                          <p className="font-medium">Bank Transfer</p>
                          <p className="text-sm text-muted-foreground">Direct bank payment</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Top Ups Tab */}
        <TabsContent value="top-ups" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Top-up History</CardTitle>
                  <CardDescription>View all account top-up transactions</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm" onClick={loadAllTransactions}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  columns={transactionColumns}
                  data={topUps}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Expenditures Tab */}
        <TabsContent value="expenditures" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Expenditure History</CardTitle>
                  <CardDescription>View all account debit transactions and service usage</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm" onClick={loadAllTransactions}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  columns={transactionColumns}
                  data={expenditures}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Refunds Tab */}
        <TabsContent value="refunds" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Refund History</CardTitle>
                  <CardDescription>View all refund transactions and credits</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm" onClick={loadAllTransactions}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  columns={transactionColumns}
                  data={refunds}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Billing;
