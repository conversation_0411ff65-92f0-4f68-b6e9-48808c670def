import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  CreditCard, 
  Download, 
  DollarSign,
  TrendingUp,
  Calendar,
  Receipt,
  Filter,
  Eye,
  FileText
} from "lucide-react";

const Billing = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);

  // Mock billing data
  const invoices = [
    {
      id: "INV-2024-001",
      period: "January 2024",
      amount: "$1,245.50",
      status: "Paid",
      dueDate: "2024-02-15",
      paidDate: "2024-02-10",
    },
    {
      id: "INV-2023-012",
      period: "December 2023",
      amount: "$1,180.25",
      status: "Paid",
      dueDate: "2024-01-15",
      paidDate: "2024-01-12",
    },
    {
      id: "INV-2023-011",
      period: "November 2023",
      amount: "$1,320.75",
      status: "Paid",
      dueDate: "2023-12-15",
      paidDate: "2023-12-08",
    },
  ];

  const usageData = [
    { service: "Bulk SMS", usage: "48,500 messages", cost: "$485.00" },
    { service: "Premium SMS", usage: "12,300 messages", cost: "$369.00" },
    { service: "Shortcode", usage: "8,900 messages", cost: "$267.00" },
    { service: "Alphanumeric", usage: "5,200 messages", cost: "$156.00" },
    { service: "USSD", usage: "2,100 sessions", cost: "$63.00" },
    { service: "Airtime", usage: "KES 45,200", cost: "$452.00" },
  ];

  const columns = [
    {
      accessorKey: "id",
      header: "Invoice ID",
      cell: ({ row }: any) => (
        <span className="font-mono">{row.getValue("id")}</span>
      ),
    },
    {
      accessorKey: "period",
      header: "Billing Period",
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }: any) => (
        <span className="font-medium text-green-600">{row.getValue("amount")}</span>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        return (
          <Badge variant={status === "Paid" ? "default" : "destructive"}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "dueDate",
      header: "Due Date",
    },
    {
      accessorKey: "paidDate",
      header: "Paid Date",
      cell: ({ row }: any) => (
        <span className="text-sm text-muted-foreground">{row.getValue("paidDate")}</span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <CreditCard className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Billing & Payments</h1>
            <p className="text-muted-foreground">Manage your billing information and payment history</p>
          </div>
        </div>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          Download Statement
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Current Balance</p>
                <p className="text-2xl font-bold">$2,450.75</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Receipt className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">$1,245.50</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Last Month</p>
                <p className="text-2xl font-bold">$1,180.25</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Next Due</p>
                <p className="text-2xl font-bold">Feb 15</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="usage">Usage Details</TabsTrigger>
          <TabsTrigger value="payment">Payment Methods</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Current Month Usage</CardTitle>
                <CardDescription>January 2024 service usage breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {usageData.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{item.service}</p>
                        <p className="text-sm text-muted-foreground">{item.usage}</p>
                      </div>
                      <span className="font-medium text-green-600">{item.cost}</span>
                    </div>
                  ))}
                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between font-medium">
                      <span>Total</span>
                      <span className="text-lg text-green-600">$1,792.00</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment History</CardTitle>
                <CardDescription>Recent payment transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { date: "2024-02-10", amount: "$1,245.50", method: "Credit Card", status: "Completed" },
                    { date: "2024-01-12", amount: "$1,180.25", method: "Bank Transfer", status: "Completed" },
                    { date: "2023-12-08", amount: "$1,320.75", method: "Credit Card", status: "Completed" },
                  ].map((payment, index) => (
                    <div key={index} className="flex items-center justify-between py-2 border-b">
                      <div>
                        <p className="font-medium">{payment.amount}</p>
                        <p className="text-sm text-muted-foreground">{payment.date} • {payment.method}</p>
                      </div>
                      <Badge variant="outline" className="text-green-600">
                        {payment.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="invoices" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Invoice History</CardTitle>
                  <CardDescription>View and download your invoices</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable 
                columns={[
                  ...columns,
                  {
                    id: "actions",
                    header: "Actions",
                    cell: ({ row }: any) => (
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    ),
                  }
                ]} 
                data={invoices} 
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Usage Report</CardTitle>
              <CardDescription>Comprehensive breakdown of service usage and costs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {usageData.map((service, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{service.service}</h4>
                      <span className="font-medium text-green-600">{service.cost}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                      <div>Usage: {service.usage}</div>
                      <div>Rate: $0.01 per unit</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>Manage your payment methods</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <CreditCard className="h-8 w-8 text-blue-500" />
                        <div>
                          <p className="font-medium">•••• •••• •••• 4242</p>
                          <p className="text-sm text-muted-foreground">Expires 12/25</p>
                        </div>
                      </div>
                      <Badge variant="outline">Primary</Badge>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full">
                    Add Payment Method
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Billing Information</CardTitle>
                <CardDescription>Your billing address and details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <p className="font-medium">Company Name</p>
                    <p className="text-muted-foreground">Liden Technologies Ltd</p>
                  </div>
                  <div>
                    <p className="font-medium">Address</p>
                    <p className="text-muted-foreground">
                      123 Business Street<br />
                      Nairobi, Kenya<br />
                      00100
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Tax ID</p>
                    <p className="text-muted-foreground">P051234567M</p>
                  </div>
                  <Button variant="outline" size="sm" className="mt-4">
                    Update Information
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Billing;
