import { useState, useEffect, useCallback } from "react"
import { useNavigate } from "react-router-dom"
import { Search, Filter, Download, RefreshCw, Plus, Eye, Edit, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DataTable, Column } from "@/components/DataTable"
import { LidenAPI } from "@/lib/api-index"
import { User, UserRole, UserParams } from "@/lib/api-types"
import { LoadingSpinner } from "@/components/LoadingSpinner"
import { AddUserModal } from "@/components/AddUserModal"
import { EditUserModal } from "@/components/EditUserModal"

export default function UserView() {
  const navigate = useNavigate()
  const [users, setUsers] = useState<User[]>([])
  const [userRoles, setUserRoles] = useState<UserRole[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedRole, setSelectedRole] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const pageSize = 20

  // Fetch users from API
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true)
      const offset = ((currentPage - 1) * pageSize) + 1

      const params: UserParams = {
        limit: pageSize,
        offset: offset,
      }

      // Only add parameters if they have values
      if (selectedRole && selectedRole !== "all") {
        params.role_id = selectedRole
      }
      if (selectedStatus && selectedStatus !== "all") {
        params.account_status = selectedStatus
      }

      const response = await LidenAPI.userManagement.getUsers(params)

      if (response.success && response.data) {
        const userData = response.data.data || []
        setUsers(userData)
        setTotalCount(parseInt(response.data.total_count) || 0)
      } else {
        setUsers([])
        setTotalCount(0)
      }
    } catch (error) {
      console.error("Failed to fetch users:", error)
      setUsers([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }, [currentPage, selectedRole, selectedStatus])

  // Fetch user roles
  const fetchUserRoles = useCallback(async () => {
    try {
      const response = await LidenAPI.userManagement.getUserRoles()

      if (response.success && response.data) {
        const rolesData = response.data.data || []
        setUserRoles(rolesData)
      } else {
        setUserRoles([])
      }
    } catch (error) {
      console.error("Failed to fetch user roles:", error)
      setUserRoles([])
    }
  }, [])

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  useEffect(() => {
    fetchUserRoles()
  }, [fetchUserRoles])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleRefresh = () => {
    fetchUsers()
    fetchUserRoles()
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log("Export users")
  }

  const handleAddUser = () => {
    setShowAddModal(true)
  }

  const handleViewUser = (user: User) => {
    navigate(`/settings/user-management/${user.user_id}`)
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setShowEditModal(true)
  }

  const handleDeleteUser = async (user: User) => {
    if (confirm(`Are you sure you want to delete user ${user.full_names}?`)) {
      try {
        const response = await LidenAPI.userManagement.deleteUser(user.user_id)
        if (response.success) {
          fetchUsers() // Refresh the list
        }
      } catch (error) {
        console.error("Failed to delete user:", error)
      }
    }
  }

  // Filter users based on search query
  const filteredUsers = users.filter(user =>
    user.full_names.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email_address.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.msisdn.includes(searchQuery) ||
    user.client_name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getStatusBadge = (status: string) => {
    return status === "1" ? (
      <Badge variant="default">Active</Badge>
    ) : (
      <Badge variant="destructive">Inactive</Badge>
    )
  }

  const columns: Column<User>[] = [
    {
      key: "full_names",
      header: "Name",
      render: (user) => (
        <div>
          <div className="font-medium">{user.full_names}</div>
          <div className="text-sm text-muted-foreground">
            {user.first_name} {user.middle_name} {user.sur_name}
          </div>
        </div>
      ),
    },
    {
      key: "email_address",
      header: "Email",
      render: (user) => (
        <div className="font-medium">{user.email_address}</div>
      ),
    },
    {
      key: "msisdn",
      header: "Phone",
      render: (user) => (
        <div>
          <div className="font-medium">{user.msisdn}</div>
          <div className="text-sm text-muted-foreground">{user.network}</div>
        </div>
      ),
    },
    {
      key: "role_name",
      header: "Role",
      render: (user) => (
        <Badge variant="outline">{user.role_name}</Badge>
      ),
    },
    {
      key: "client_name",
      header: "Client",
      render: (user) => (
        <div className="font-medium">{user.client_name}</div>
      ),
    },
    {
      key: "account_status",
      header: "Status",
      render: (user) => getStatusBadge(user.account_status),
    },
    {
      key: "last_login_date",
      header: "Last Login",
      render: (user) => (
        <div className="text-sm">
          {user.last_login_date ? (
            <>
              <div>{new Date(user.last_login_date).toLocaleDateString()}</div>
              <div className="text-muted-foreground">{new Date(user.last_login_date).toLocaleTimeString()}</div>
            </>
          ) : (
            <span className="text-muted-foreground">Never</span>
          )}
        </div>
      ),
    },
    {
      key: "created_at",
      header: "Created",
      render: (user) => (
        <div className="text-sm">
          {new Date(user.created_at).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: "actions",
      header: "Actions",
      render: (user) => (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewUser(user)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditUser(user)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteUser(user)}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Manage system users, roles, and permissions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleAddUser}>
            <Plus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center gap-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search users, email, phone..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedRole} onValueChange={setSelectedRole}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              {(Array.isArray(userRoles) ? userRoles : []).map((role) => (
                <SelectItem key={role.role_id} value={role.role_id}>
                  {role.role_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="1">Active</SelectItem>
              <SelectItem value="0">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Data Table */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : (
        <DataTable
          data={filteredUsers}
          columns={columns}
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / pageSize)}
          onPageChange={handlePageChange}
          loading={loading}
        />
      )}

      {/* Modals */}
      <AddUserModal
        open={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          fetchUsers()
          setShowAddModal(false)
        }}
      />

      <EditUserModal
        open={showEditModal}
        onClose={() => {
          setShowEditModal(false)
          setSelectedUser(null)
        }}
        onSuccess={() => {
          fetchUsers()
          setShowEditModal(false)
          setSelectedUser(null)
        }}
        user={selectedUser}
      />
    </div>
  )
}
