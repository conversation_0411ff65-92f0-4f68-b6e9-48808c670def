import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom"
import { ArrowLeft, Edit, UserX, Clock, Shield, Activity } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { LidenAPI } from "@/lib/api-index"
import { User, UserPermission } from "@/lib/api-types"
import { LoadingSpinner } from "@/components/LoadingSpinner"

export default function UserDetails() {
  const { userId } = useParams<{ userId: string }>()
  const navigate = useNavigate()
  const [user, setUser] = useState<User | null>(null)
  const [permissions, setPermissions] = useState<UserPermission[]>([])
  const [activityLogs, setActivityLogs] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [permissionsLoading, setPermissionsLoading] = useState(false)

  useEffect(() => {
    if (userId) {
      fetchUserDetails()
      fetchUserPermissions()
      fetchUserActivityLogs()
    }
  }, [userId])

  const fetchUserDetails = async () => {
    try {
      setLoading(true)
      const response = await LidenAPI.userManagement.getUser(userId!)
      if (response.success && response.data) {
        setUser(response.data)
      }
    } catch (error) {
      console.error("Failed to fetch user details:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchUserPermissions = async () => {
    try {
      setPermissionsLoading(true)
      const response = await LidenAPI.userManagement.getUserPermissions(userId!)
      if (response.success && response.data) {
        setPermissions(response.data.data || [])
      }
    } catch (error) {
      console.error("Failed to fetch user permissions:", error)
    } finally {
      setPermissionsLoading(false)
    }
  }

  const fetchUserActivityLogs = async () => {
    try {
      const response = await LidenAPI.userManagement.getUserActivityLogs(userId!)
      if (response.success && response.data) {
        setActivityLogs(response.data.data || [])
      }
    } catch (error) {
      console.error("Failed to fetch user activity logs:", error)
    }
  }

  const handleEditUser = () => {
    // TODO: Open edit user modal
    console.log("Edit user:", user?.user_id)
  }

  const handleDisableUser = async () => {
    if (!user) return
    
    const action = user.account_status === "1" ? "disable" : "enable"
    if (confirm(`Are you sure you want to ${action} this user?`)) {
      try {
        const response = await LidenAPI.userManagement.updateUser(user.user_id, {
          account_status: user.account_status === "1" ? "0" : "1"
        })
        if (response.success) {
          fetchUserDetails() // Refresh user data
        }
      } catch (error) {
        console.error(`Failed to ${action} user:`, error)
      }
    }
  }

  const handlePermissionToggle = async (permissionId: string, currentStatus: boolean) => {
    try {
      const response = await LidenAPI.userManagement.updateUserPermission(userId!, permissionId, !currentStatus)
      if (response.success) {
        fetchUserPermissions() // Refresh permissions
      }
    } catch (error) {
      console.error("Failed to update permission:", error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">User not found</p>
        <Button onClick={() => navigate("/settings/user-management")} className="mt-4">
          Back to Users
        </Button>
      </div>
    )
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate("/settings/user-management")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight">User Details</h1>
          <p className="text-muted-foreground">
            View and manage user information, permissions, and activity
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleEditUser}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button 
            variant={user.account_status === "1" ? "destructive" : "default"}
            onClick={handleDisableUser}
          >
            <UserX className="h-4 w-4 mr-2" />
            {user.account_status === "1" ? "Disable" : "Enable"}
          </Button>
        </div>
      </div>

      {/* User Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarFallback className="text-lg">
                {getInitials(user.full_names)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-2xl font-bold">{user.full_names}</h2>
              <p className="text-muted-foreground">{user.email_address}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant={user.account_status === "1" ? "default" : "destructive"}>
                  {user.account_status === "1" ? "Active" : "Inactive"}
                </Badge>
                <Badge variant="outline">{user.role_name}</Badge>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Contact Information</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-muted-foreground">Phone:</span> {user.msisdn}
                </div>
                <div>
                  <span className="text-muted-foreground">Network:</span> {user.network}
                </div>
                <div>
                  <span className="text-muted-foreground">Client:</span> {user.client_name}
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Account Details</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-muted-foreground">User ID:</span> {user.user_id}
                </div>
                <div>
                  <span className="text-muted-foreground">Role ID:</span> {user.role_id}
                </div>
                <div>
                  <span className="text-muted-foreground">Created:</span> {new Date(user.created_at).toLocaleDateString()}
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Activity</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-muted-foreground">Last Login:</span>{" "}
                  {user.last_login_date ? new Date(user.last_login_date).toLocaleDateString() : "Never"}
                </div>
                <div>
                  <span className="text-muted-foreground">Login Count:</span> {user.login_count || 0}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for Permissions and Activity */}
      <Tabs defaultValue="permissions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Permissions
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Activity Logs
          </TabsTrigger>
        </TabsList>

        <TabsContent value="permissions">
          <Card>
            <CardHeader>
              <CardTitle>User Permissions</CardTitle>
            </CardHeader>
            <CardContent>
              {permissionsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <div className="space-y-4">
                  {permissions.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">
                      No permissions found for this user
                    </p>
                  ) : (
                    permissions.map((permission) => (
                      <div key={permission.permission_id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{permission.permission_name}</h4>
                          <p className="text-sm text-muted-foreground">{permission.permission_description}</p>
                        </div>
                        <Switch
                          checked={permission.status === "1"}
                          onCheckedChange={() => handlePermissionToggle(permission.permission_id, permission.status === "1")}
                        />
                      </div>
                    ))
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>Activity Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activityLogs.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    No activity logs found for this user
                  </p>
                ) : (
                  activityLogs.map((log, index) => (
                    <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0">
                        <Clock className="h-5 w-5 text-muted-foreground" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{log.action}</h4>
                        <p className="text-sm text-muted-foreground">{log.description}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {new Date(log.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
