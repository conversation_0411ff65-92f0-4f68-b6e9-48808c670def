import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/DataTable";
import { 
  CreditCard, 
  Download,
  DollarSign,
  Globe,
  MessageSquare,
  TrendingDown,
  Info
} from "lucide-react";

const BulkRateCard = () => {
  // Mock data for bulk rates
  const bulkRates = [
    { country: "Kenya", network: "Safaricom", rate: "$0.015", volume: "1-10K", type: "Standard" },
    { country: "Kenya", network: "Safaricom", rate: "$0.012", volume: "10K-100K", type: "Bulk" },
    { country: "Kenya", network: "Safaricom", rate: "$0.010", volume: "100K+", type: "Enterprise" },
    { country: "Kenya", network: "Airtel", rate: "$0.018", volume: "1-10K", type: "Standard" },
    { country: "Kenya", network: "Airtel", rate: "$0.015", volume: "10K-100K", type: "Bulk" },
    { country: "Uganda", network: "MTN", rate: "$0.020", volume: "1-10K", type: "Standard" },
    { country: "Tanzania", network: "Vodacom", rate: "$0.022", volume: "1-10K", type: "Standard" },
  ];

  const columns = [
    {
      accessorKey: "country",
      header: "Country",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <Globe className="h-4 w-4 text-blue-500" />
          <span>{row.getValue("country")}</span>
        </div>
      ),
    },
    {
      accessorKey: "network",
      header: "Network",
      cell: ({ row }: any) => (
        <Badge variant="outline">{row.getValue("network")}</Badge>
      ),
    },
    {
      accessorKey: "volume",
      header: "Volume Range",
    },
    {
      accessorKey: "rate",
      header: "Rate per SMS",
      cell: ({ row }: any) => (
        <span className="font-medium text-green-600">{row.getValue("rate")}</span>
      ),
    },
    {
      accessorKey: "type",
      header: "Plan Type",
      cell: ({ row }: any) => {
        const type = row.getValue("type");
        const colors = {
          Standard: "default",
          Bulk: "secondary",
          Enterprise: "outline",
        };
        return (
          <Badge variant={colors[type as keyof typeof colors] as any}>
            {type}
          </Badge>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <CreditCard className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Bulk Rate Card</h1>
            <p className="text-muted-foreground">View SMS pricing and bulk discount rates</p>
          </div>
        </div>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          Download Rate Card
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Best Rate</p>
                <p className="text-2xl font-bold">$0.010</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Countries</p>
                <p className="text-2xl font-bold">15</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Networks</p>
                <p className="text-2xl font-bold">45</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingDown className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Max Discount</p>
                <p className="text-2xl font-bold">33%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="rates" className="space-y-4">
        <TabsList>
          <TabsTrigger value="rates">Current Rates</TabsTrigger>
          <TabsTrigger value="calculator">Rate Calculator</TabsTrigger>
          <TabsTrigger value="discounts">Volume Discounts</TabsTrigger>
        </TabsList>

        <TabsContent value="rates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SMS Rates by Country & Network</CardTitle>
              <CardDescription>Current pricing for SMS delivery across different networks</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={bulkRates} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calculator" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Cost Calculator</CardTitle>
                <CardDescription>Calculate SMS costs based on volume and destination</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-8 text-muted-foreground">
                  <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Rate calculator coming soon</p>
                  <p className="text-sm">Calculate costs for different volumes and destinations</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Volume Pricing</CardTitle>
                <CardDescription>Pricing tiers based on monthly volume</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { tier: "Starter", volume: "1 - 10,000", discount: "0%", rate: "$0.015" },
                    { tier: "Business", volume: "10,001 - 100,000", discount: "20%", rate: "$0.012" },
                    { tier: "Enterprise", volume: "100,001+", discount: "33%", rate: "$0.010" },
                  ].map((tier, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{tier.tier}</h4>
                        <Badge variant="outline">{tier.discount} off</Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                        <div>Volume: {tier.volume}</div>
                        <div>Rate: {tier.rate}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="discounts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Volume Discount Structure</CardTitle>
              <CardDescription>Automatic discounts based on monthly SMS volume</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900 dark:text-blue-100">Volume Discounts</h4>
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        Discounts are automatically applied based on your monthly SMS volume. 
                        Higher volumes qualify for better rates across all destinations.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-blue-600 mb-2">0%</div>
                      <div className="font-medium mb-1">Standard Rate</div>
                      <div className="text-sm text-muted-foreground">1 - 10,000 SMS/month</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-green-600 mb-2">20%</div>
                      <div className="font-medium mb-1">Business Discount</div>
                      <div className="text-sm text-muted-foreground">10,001 - 100,000 SMS/month</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-purple-600 mb-2">33%</div>
                      <div className="font-medium mb-1">Enterprise Discount</div>
                      <div className="text-sm text-muted-foreground">100,001+ SMS/month</div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default BulkRateCard;
