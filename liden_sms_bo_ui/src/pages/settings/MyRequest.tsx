import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { useDateRangePickerWithQuickSelect } from "@/hooks/useDateRangePicker";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { LidenAPI } from "@/lib/api-index";
import { 
  Settings, 
  Plus, 
  Clock,
  CheckCircle,
  XCircle,
  FileText,
  Send,
  Filter,
  RefreshCw,
  ChevronDown,
  Network,
  MessageSquare,
  Code
} from "lucide-react";

// Interface definitions based on API responses
interface SenderIdRequest {
  total_count: string;
  id: string;
  sender_id: string;
  sender_type: string;
  sStatus: string;
  short_code: string;
  status: string;
  client_id: string;
  client_name: string;
  client_email: string;
}

interface USSDApp {
  total_count: string;
  id: string;
  c_serviceId: string;
  system_name: string;
  status: string;
  created_at: string;
  created_by: string;
  approved_by: string | null;
}

interface USSDType {
  type_id: string;
  name: string;
  description: string;
}

interface SMSNetwork {
  network_id: string;
  network_name: string;
  network_code: string;
  country_code: string;
  country: string;
  sms_rate: string;
  minimum_rate: string;
  is_local: string;
  sender_id_cost: string;
  short_code_cost: string;
}

const MyRequest = () => {
  // Data states
  const [senderIds, setSenderIds] = useState<SenderIdRequest[]>([]);
  const [ussdApps, setUssdApps] = useState<USSDApp[]>([]);
  const [ussdTypes, setUssdTypes] = useState<USSDType[]>([]);
  const [smsNetworks, setSmsNetworks] = useState<SMSNetwork[]>([]);
  
  // Loading states
  const [loading, setLoading] = useState(false);
  const [senderIdsLoading, setSenderIdsLoading] = useState(false);
  const [ussdAppsLoading, setUssdAppsLoading] = useState(false);
  const [ussdTypesLoading, setUssdTypesLoading] = useState(false);
  const [smsNetworksLoading, setSmsNetworksLoading] = useState(false);

  // Date picker hook
  const datePicker = useDateRangePickerWithQuickSelect({
    onDateRangeChange: () => {
      fetchAllMyRequests();
    }
  });

  // Fetch Sender IDs
  const fetchSenderIds = async () => {
    try {
      setSenderIdsLoading(true);
      const dateParams = datePicker.getAPIDateParams();
      const params = {
        shortCode: "",
        offset: "",
        sort: "",
        export: "",
        limit: "",
        clientId: "46", // Based on your API example
        ...dateParams,
      };

      const response = await LidenAPI.client.getSenderIds(params);
      // Handle the actual API response structure: { code: "Success", data: { data: [...] } }
      if (response.success && response.data?.data?.data) {
        setSenderIds(response.data.data.data || []);
      } else if (response.data?.code === "Success" && response.data?.data?.data) {
        setSenderIds(response.data.data.data || []);
      }
    } catch (error) {
      console.error("Failed to fetch sender IDs:", error);
      setSenderIds([]);
    } finally {
      setSenderIdsLoading(false);
    }
  };

  // Fetch USSD Apps
  const fetchUssdApps = async () => {
    try {
      setUssdAppsLoading(true);
      const dateParams = datePicker.getAPIDateParams();
      const params = {
        ...dateParams,
      };

      const response = await LidenAPI.ussd.getApps(params);
      // Handle the actual API response structure: { code: "Success", data: { data: { data: [...] } } }
      if (response.success && response.data?.data?.data?.data) {
        setUssdApps(response.data.data.data.data || []);
      } else if (response.data?.code === "Success" && response.data?.data?.data?.data) {
        setUssdApps(response.data.data.data.data || []);
      }
    } catch (error) {
      console.error("Failed to fetch USSD apps:", error);
      setUssdApps([]);
    } finally {
      setUssdAppsLoading(false);
    }
  };

  // Fetch USSD Types
  const fetchUssdTypes = async () => {
    try {
      setUssdTypesLoading(true);
      const dateParams = datePicker.getAPIDateParams();
      const params = {
        ...dateParams,
      };

      const response = await LidenAPI.ussd.getTypes(params);
      // Handle the actual API response structure: { code: "Success", data: { data: [...] } }
      if (response.success && response.data?.data?.data) {
        setUssdTypes(response.data.data.data || []);
      } else if (response.data?.code === "Success" && response.data?.data?.data) {
        setUssdTypes(response.data.data.data || []);
      }
    } catch (error) {
      console.error("Failed to fetch USSD types:", error);
      setUssdTypes([]);
    } finally {
      setUssdTypesLoading(false);
    }
  };

  // Fetch SMS Networks
  const fetchSmsNetworks = async () => {
    try {
      setSmsNetworksLoading(true);
      const params = {
        limit: 20,
      };

      const response = await LidenAPI.smsNetworks.getNetworks(params);
      // Handle the actual API response structure: { code: "Success", data: { data: { data: [...] } } }
      if (response.success && response.data?.data?.data?.data) {
        setSmsNetworks(response.data.data.data.data || []);
      } else if (response.data?.code === "Success" && response.data?.data?.data?.data) {
        setSmsNetworks(response.data.data.data.data || []);
      }
    } catch (error) {
      console.error("Failed to fetch SMS networks:", error);
      setSmsNetworks([]);
    } finally {
      setSmsNetworksLoading(false);
    }
  };

  // Fetch all data for My Requests view
  const fetchAllMyRequests = async () => {
    setLoading(true);
    await Promise.all([
      fetchSenderIds(),
      fetchUssdApps(),
      fetchUssdTypes(),
      fetchSmsNetworks(),
    ]);
    setLoading(false);
  };

  // Load data on component mount
  useEffect(() => {
    fetchAllMyRequests();
  }, []);

  // Helper functions for status badges
  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { variant: string; className: string }> = {
      "1": { variant: "default", className: "bg-green-600 text-white" },
      "4": { variant: "secondary", className: "bg-blue-600 text-white" },
      "13": { variant: "outline", className: "bg-yellow-600 text-white" },
    };
    
    const config = statusMap[status] || { variant: "outline", className: "bg-gray-600 text-white" };
    return (
      <Badge variant="outline" className={config.className}>
        {status === "1" ? "Active" : status === "4" ? "Pending" : status === "13" ? "Approved" : status}
      </Badge>
    );
  };

  const getServiceTypeBadge = (type: string) => {
    const typeColors: Record<string, string> = {
      'ALPHANUMERIC': 'bg-blue-600',
      'MT': 'bg-green-600',
      'USSD': 'bg-purple-600',
      'SMS': 'bg-orange-600'
    };
    
    return (
      <Badge variant="outline" className={`${typeColors[type] || 'bg-gray-600'} text-white border-0`}>
        {type}
      </Badge>
    );
  };

  const handleRefresh = () => {
    fetchAllMyRequests();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>Settings</span>
          <span>›</span>
          <span>Services Request</span>
          <span>›</span>
          <span className="text-foreground">My Request</span>
        </div>
        
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Sender IDs</p>
                <p className="text-2xl font-bold">{senderIds.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Code className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">USSD Apps</p>
                <p className="text-2xl font-bold">{ussdApps.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">USSD Types</p>
                <p className="text-2xl font-bold">{ussdTypes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Network className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">SMS Networks</p>
                <p className="text-2xl font-bold">{smsNetworks.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Date Range Filter */}
        <Card>
          <CardHeader>
            <CardTitle>Filter by Date Range</CardTitle>
          </CardHeader>
          <CardContent>
            <DatePickerWithRange
              date={datePicker.dateRange}
              onDateChange={datePicker.setDateRange}
              placeholder="Select date range to filter requests"
              className="w-full max-w-md"
            />
          </CardContent>
        </Card>

        {/* Sender IDs Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Sender IDs ({senderIds.length})
            </CardTitle>
            <CardDescription>Your registered sender IDs and their status</CardDescription>
          </CardHeader>
          <CardContent>
            {senderIdsLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Sender ID</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Short Code</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Client</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {senderIds.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No sender IDs found
                      </TableCell>
                    </TableRow>
                  ) : (
                    senderIds.map((senderId) => (
                      <TableRow key={senderId.id}>
                        <TableCell className="font-mono">{senderId.id}</TableCell>
                        <TableCell className="font-medium">{senderId.sender_id}</TableCell>
                        <TableCell>{getServiceTypeBadge(senderId.sender_type)}</TableCell>
                        <TableCell>{senderId.short_code}</TableCell>
                        <TableCell>{getStatusBadge(senderId.status)}</TableCell>
                        <TableCell className="text-sm text-muted-foreground">{senderId.client_name}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* USSD Apps Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              USSD Apps ({ussdApps.length})
            </CardTitle>
            <CardDescription>Your USSD applications and their configurations</CardDescription>
          </CardHeader>
          <CardContent>
            {ussdAppsLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Service ID</TableHead>
                    <TableHead>System Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Created By</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {ussdApps.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No USSD apps found
                      </TableCell>
                    </TableRow>
                  ) : (
                    ussdApps.map((app) => (
                      <TableRow key={app.id}>
                        <TableCell className="font-mono">{app.id}</TableCell>
                        <TableCell>{app.c_serviceId}</TableCell>
                        <TableCell className="font-medium">{app.system_name}</TableCell>
                        <TableCell>{getStatusBadge(app.status)}</TableCell>
                        <TableCell>{new Date(app.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>{app.created_by}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* USSD Types Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              USSD Types ({ussdTypes.length})
            </CardTitle>
            <CardDescription>Available USSD service types and their descriptions</CardDescription>
          </CardHeader>
          <CardContent>
            {ussdTypesLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {ussdTypes.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-8 text-muted-foreground">
                        No USSD types found
                      </TableCell>
                    </TableRow>
                  ) : (
                    ussdTypes.map((type) => (
                      <TableRow key={type.type_id}>
                        <TableCell className="font-mono">{type.type_id}</TableCell>
                        <TableCell className="font-medium">{type.name}</TableCell>
                        <TableCell className="text-sm text-muted-foreground">{type.description}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* SMS Networks Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              SMS Networks ({smsNetworks.length})
            </CardTitle>
            <CardDescription>Available SMS networks and their configurations</CardDescription>
          </CardHeader>
          <CardContent>
            {smsNetworksLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Network ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Country</TableHead>
                    <TableHead>SMS Rate</TableHead>
                    <TableHead>Local</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {smsNetworks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No SMS networks found
                      </TableCell>
                    </TableRow>
                  ) : (
                    smsNetworks.map((network) => (
                      <TableRow key={network.network_id}>
                        <TableCell className="font-mono">{network.network_id}</TableCell>
                        <TableCell className="font-medium">{network.network_name}</TableCell>
                        <TableCell>{network.network_code}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="bg-blue-600 text-white">
                            {network.country}
                          </Badge>
                        </TableCell>
                        <TableCell>{network.sms_rate}</TableCell>
                        <TableCell>
                          <Badge variant={network.is_local === "1" ? "default" : "secondary"}>
                            {network.is_local === "1" ? "Yes" : "No"}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MyRequest;
