import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  Receipt, 
  Download,
  Eye,
  DollarSign,
  Calendar,
  CheckCircle,
  Clock,
  Filter
} from "lucide-react";

const Invoice = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);

  const invoices = [
    {
      id: "INV-2024-001",
      amount: "$1,245.50",
      status: "Paid",
      dueDate: "2024-02-15",
      issueDate: "2024-01-15",
      period: "January 2024",
    },
    {
      id: "INV-2023-012",
      amount: "$1,180.25",
      status: "Paid",
      dueDate: "2024-01-15",
      issueDate: "2023-12-15",
      period: "December 2023",
    },
    {
      id: "INV-2023-011",
      amount: "$1,320.75",
      status: "Overdue",
      dueDate: "2023-12-15",
      issueDate: "2023-11-15",
      period: "November 2023",
    },
  ];

  const columns = [
    {
      accessorKey: "id",
      header: "Invoice ID",
      cell: ({ row }: any) => (
        <span className="font-mono">{row.getValue("id")}</span>
      ),
    },
    {
      accessorKey: "period",
      header: "Billing Period",
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }: any) => (
        <span className="font-medium text-green-600">{row.getValue("amount")}</span>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        const variants = {
          Paid: "default",
          Pending: "secondary",
          Overdue: "destructive",
        };
        return (
          <Badge variant={variants[status as keyof typeof variants] as any}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "dueDate",
      header: "Due Date",
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Receipt className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Invoices</h1>
            <p className="text-muted-foreground">View and manage your invoices</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Invoiced</p>
                <p className="text-2xl font-bold">$3,746.50</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Paid</p>
                <p className="text-2xl font-bold">$2,425.75</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold">$1,320.75</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">$1,245.50</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Invoice History</CardTitle>
              <CardDescription>View and download your invoices</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <DateRangePicker
                value={selectedDateRange}
                onChange={setSelectedDateRange}
                placeholder="Select date range"
              />
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable columns={columns} data={invoices} />
        </CardContent>
      </Card>
    </div>
  );
};

export default Invoice;
