import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { LidenAPI } from "@/lib/api-index";
import {
  Settings,
  Plus,
  Send,
  FileText,
  MessageSquare,
  Code,
  Network,
  Zap,
  HelpCircle,
  ChevronRight,
  ChevronLeft,
  CheckCircle,
  ArrowRight
} from "lucide-react";

// Interface definitions
interface SMSNetwork {
  network_id: string;
  network_name: string;
  network_code: string;
  country_code: string;
  country: string;
  sms_rate: string;
  minimum_rate: string;
  is_local: string;
  sender_id_cost: string;
  short_code_cost: string;
}

interface USSDType {
  type_id: string;
  name: string;
  description: string;
}

const ApplyRequest = () => {
  // Wizard states
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedService, setSelectedService] = useState("");

  // Form states
  const [serviceType, setServiceType] = useState("");
  const [priority, setPriority] = useState("");
  const [requestTitle, setRequestTitle] = useState("");
  const [requestDescription, setRequestDescription] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Service-specific form states
  const [senderId, setSenderId] = useState("");
  const [selectedNetwork, setSelectedNetwork] = useState("");
  const [selectedUssdType, setSelectedUssdType] = useState("");
  const [businessPurpose, setBusinessPurpose] = useState("");
  const [expectedVolume, setExpectedVolume] = useState("");
  const [targetCountries, setTargetCountries] = useState("");

  // Data states
  const [smsNetworks, setSmsNetworks] = useState<SMSNetwork[]>([]);
  const [ussdTypes, setUssdTypes] = useState<USSDType[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch SMS Networks
  const fetchSmsNetworks = async () => {
    try {
      setLoading(true);
      const response = await LidenAPI.smsNetworks.getNetworks({ limit: 20 });
      if (response.success && response.data?.data?.data?.data) {
        setSmsNetworks(response.data.data.data.data || []);
      } else if (response.data?.code === "Success" && response.data?.data?.data?.data) {
        setSmsNetworks(response.data.data.data.data || []);
      }
    } catch (error) {
      console.error("Failed to fetch SMS networks:", error);
      setSmsNetworks([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch USSD Types
  const fetchUssdTypes = async () => {
    try {
      setLoading(true);
      const response = await LidenAPI.ussd.getTypes();
      if (response.success && response.data?.data?.data) {
        setUssdTypes(response.data.data.data || []);
      } else if (response.data?.code === "Success" && response.data?.data?.data) {
        setUssdTypes(response.data.data.data || []);
      }
    } catch (error) {
      console.error("Failed to fetch USSD types:", error);
      setUssdTypes([]);
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchSmsNetworks();
    fetchUssdTypes();
  }, []);

  const handleSubmitRequest = async () => {
    if (!serviceType || !priority || !requestTitle || !requestDescription) {
      alert("Please fill in all fields");
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare request data based on service type
      const requestData = {
        serviceType,
        priority,
        requestTitle,
        requestDescription,
        senderId: senderId || undefined,
        selectedNetwork: selectedNetwork || undefined,
        selectedUssdType: selectedUssdType || undefined,
        businessPurpose: businessPurpose || undefined,
        expectedVolume: expectedVolume || undefined,
        targetCountries: targetCountries || undefined,
      };

      console.log("Submitting request:", requestData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      alert("Request submitted successfully!");

      // Reset form
      resetForm();
    } catch (error) {
      console.error("Failed to submit request:", error);
      alert("Failed to submit request. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setCurrentStep(1);
    setSelectedService("");
    setServiceType("");
    setPriority("");
    setRequestTitle("");
    setRequestDescription("");
    setSenderId("");
    setSelectedNetwork("");
    setSelectedUssdType("");
    setBusinessPurpose("");
    setExpectedVolume("");
    setTargetCountries("");
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleServiceSelect = (service: string) => {
    setSelectedService(service);
    setServiceType(service);
    nextStep();
  };

  const serviceOptions = [
    {
      id: "sender-id",
      title: "Sender ID Application",
      description: "Apply for a custom alphanumeric sender ID for your SMS campaigns",
      icon: MessageSquare,
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      id: "shortcode",
      title: "Two-Way SMS ShortCode",
      description: "Register a shortcode for two-way SMS communication with your users",
      icon: Code,
      color: "text-green-500",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    },
    {
      id: "ussd",
      title: "USSD Services Code",
      description: "Apply for USSD code to interact with a service application from their device in real time",
      icon: Settings,
      color: "text-purple-500",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200"
    },
    {
      id: "survey",
      title: "Survey",
      description: "Survey allows you to Get real state of your services or realtime using the Survey",
      icon: FileText,
      color: "text-orange-500",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200"
    },
    {
      id: "premium-sms",
      title: "Premium SMS Shortcode",
      description: "Premium revenue by delivering entertainment messages to your users either on demand or on subscription",
      icon: Zap,
      color: "text-yellow-500",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200"
    }
  ];



  const renderStepIndicator = () => (
    <div className="flex items-center justify-center space-x-4 mb-8">
      {[1, 2, 3].map((step) => (
        <div key={step} className="flex items-center">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
            currentStep >= step
              ? 'bg-red-600 border-red-600 text-white'
              : 'border-gray-300 text-gray-400'
          }`}>
            {currentStep > step ? <CheckCircle className="h-4 w-4" /> : step}
          </div>
          {step < 3 && (
            <div className={`w-16 h-0.5 mx-2 ${
              currentStep > step ? 'bg-red-600' : 'bg-gray-300'
            }`} />
          )}
        </div>
      ))}
    </div>
  );

  const renderStep1 = () => (
    <Card>
      <CardHeader>
        <CardTitle className="text-center">Select Services</CardTitle>
        <CardDescription className="text-center">Choose the service you want to apply for</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {serviceOptions.map((service) => {
            const IconComponent = service.icon;
            return (
              <Card
                key={service.id}
                className={`cursor-pointer hover:shadow-md transition-all border-2 hover:border-red-200 ${service.bgColor}`}
                onClick={() => handleServiceSelect(service.id)}
              >
                <CardContent className="p-6 text-center">
                  <div className="flex flex-col items-center space-y-3">
                    <div className={`p-3 rounded-full ${service.bgColor} ${service.borderColor} border`}>
                      <IconComponent className={`h-8 w-8 ${service.color}`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{service.title}</h3>
                      <p className="text-sm text-muted-foreground mt-1">{service.description}</p>
                    </div>
                    <Button variant="outline" className="mt-4">
                      <ArrowRight className="h-4 w-4 ml-2" />
                      Apply
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>Settings</span>
          <span>›</span>
          <span>Services Request</span>
          <span>›</span>
          <span className="text-foreground">Apply Request</span>
        </div>
      </div>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Step 1: Service Selection */}
      {currentStep === 1 && renderStep1()}

      {/* Step 2: Application Form */}
      {currentStep === 2 && (
        <Card>
          <CardHeader>
            <CardTitle>Application</CardTitle>
            <CardDescription>
              {selectedService === "sender-id" && "Sender ID Application - Please provide your request to appropriate"}
              {selectedService === "shortcode" && "Two-Way SMS ShortCode Application"}
              {selectedService === "ussd" && "USSD Services Code Application"}
              {selectedService === "survey" && "Survey Application"}
              {selectedService === "premium-sms" && "Premium SMS Shortcode Application"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Service-specific forms */}
            {selectedService === "sender-id" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="senderId">Sender ID *</Label>
                  <Input
                    id="senderId"
                    placeholder="Enter desired sender ID"
                    value={senderId}
                    onChange={(e) => setSenderId(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="network">Select Network *</Label>
                  <Select value={selectedNetwork} onValueChange={setSelectedNetwork}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose network" />
                    </SelectTrigger>
                    <SelectContent>
                      {loading ? (
                        <SelectItem value="loading" disabled>Loading networks...</SelectItem>
                      ) : (
                        smsNetworks.map((network) => (
                          <SelectItem key={network.network_id} value={network.network_id}>
                            {network.network_name} ({network.country})
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {selectedService === "ussd" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ussdType">USSD Type *</Label>
                  <Select value={selectedUssdType} onValueChange={setSelectedUssdType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select USSD type" />
                    </SelectTrigger>
                    <SelectContent>
                      {loading ? (
                        <SelectItem value="loading" disabled>Loading types...</SelectItem>
                      ) : (
                        ussdTypes.map((type) => (
                          <SelectItem key={type.type_id} value={type.type_id}>
                            {type.name} - {type.description}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Common fields for all services */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="businessPurpose">Business Purpose *</Label>
                <Textarea
                  id="businessPurpose"
                  placeholder="Describe the business purpose for this service"
                  value={businessPurpose}
                  onChange={(e) => setBusinessPurpose(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expectedVolume">Expected Monthly Volume</Label>
                  <Input
                    id="expectedVolume"
                    placeholder="e.g., 10,000 messages"
                    value={expectedVolume}
                    onChange={(e) => setExpectedVolume(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="targetCountries">Target Countries</Label>
                  <Input
                    id="targetCountries"
                    placeholder="e.g., Kenya, Uganda, Tanzania"
                    value={targetCountries}
                    onChange={(e) => setTargetCountries(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Enter your description here"
                  value={requestDescription}
                  onChange={(e) => setRequestDescription(e.target.value)}
                  rows={4}
                />
              </div>
            </div>

            {/* Navigation buttons */}
            <div className="flex justify-between pt-4">
              <Button variant="outline" onClick={prevStep}>
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              <Button
                onClick={nextStep}
                disabled={!businessPurpose || !requestDescription}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Confirmation */}
      {currentStep === 3 && (
        <Card>
          <CardHeader>
            <CardTitle>Confirmation</CardTitle>
            <CardDescription>Review your application details before submitting</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              <div>
                <h3 className="font-semibold text-lg mb-2">Application Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Service Type:</span>
                    <p className="text-muted-foreground">
                      {serviceOptions.find(s => s.id === selectedService)?.title}
                    </p>
                  </div>

                  {senderId && (
                    <div>
                      <span className="font-medium">Sender ID:</span>
                      <p className="text-muted-foreground">{senderId}</p>
                    </div>
                  )}

                  {selectedNetwork && (
                    <div>
                      <span className="font-medium">Network:</span>
                      <p className="text-muted-foreground">
                        {smsNetworks.find(n => n.network_id === selectedNetwork)?.network_name}
                      </p>
                    </div>
                  )}

                  {selectedUssdType && (
                    <div>
                      <span className="font-medium">USSD Type:</span>
                      <p className="text-muted-foreground">
                        {ussdTypes.find(t => t.type_id === selectedUssdType)?.name}
                      </p>
                    </div>
                  )}

                  {expectedVolume && (
                    <div>
                      <span className="font-medium">Expected Volume:</span>
                      <p className="text-muted-foreground">{expectedVolume}</p>
                    </div>
                  )}

                  {targetCountries && (
                    <div>
                      <span className="font-medium">Target Countries:</span>
                      <p className="text-muted-foreground">{targetCountries}</p>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <span className="font-medium">Business Purpose:</span>
                <p className="text-muted-foreground mt-1">{businessPurpose}</p>
              </div>

              <div>
                <span className="font-medium">Description:</span>
                <p className="text-muted-foreground mt-1">{requestDescription}</p>
              </div>
            </div>

            {/* Navigation buttons */}
            <div className="flex justify-between pt-4">
              <Button variant="outline" onClick={prevStep}>
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              <Button
                onClick={handleSubmitRequest}
                disabled={isSubmitting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Application
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

//       {/* Request Form */}
//       <Card>
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <FileText className="h-5 w-5" />
//             Submit New Request
//           </CardTitle>
//           <CardDescription>Request new services or features for your account</CardDescription>
//         </CardHeader>
//         <CardContent className="space-y-6">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div className="space-y-2">
//               <Label htmlFor="serviceType">Service Type *</Label>
//               <Select value={serviceType} onValueChange={setServiceType}>
//                 <SelectTrigger>
//                   <SelectValue placeholder="Select service type" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="sender-id">Sender ID</SelectItem>
//                   <SelectItem value="shortcode">Shortcode</SelectItem>
//                   <SelectItem value="ussd">USSD Code</SelectItem>
//                   <SelectItem value="sms-network">SMS Network</SelectItem>
//                   <SelectItem value="api">API Access</SelectItem>
//                   <SelectItem value="integration">Integration Support</SelectItem>
//                   <SelectItem value="support">Technical Support</SelectItem>
//                   <SelectItem value="other">Other</SelectItem>
//                 </SelectContent>
//               </Select>
//             </div>
//             <div className="space-y-2">
//               <Label htmlFor="priority">Priority *</Label>
//               <Select value={priority} onValueChange={setPriority}>
//                 <SelectTrigger>
//                   <SelectValue placeholder="Select priority" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="low">Low</SelectItem>
//                   <SelectItem value="medium">Medium</SelectItem>
//                   <SelectItem value="high">High</SelectItem>
//                   <SelectItem value="urgent">Urgent</SelectItem>
//                 </SelectContent>
//               </Select>
//             </div>
//           </div>
          
//           <div className="space-y-2">
//             <Label htmlFor="title">Request Title *</Label>
//             <Input
//               id="title"
//               placeholder="Brief description of your request"
//               value={requestTitle}
//               onChange={(e) => setRequestTitle(e.target.value)}
//             />
//           </div>
          
//           <div className="space-y-2">
//             <Label htmlFor="description">Detailed Description *</Label>
//             <Textarea
//               id="description"
//               placeholder="Provide detailed information about your request, including specific requirements, expected outcomes, and any relevant technical details..."
//               value={requestDescription}
//               onChange={(e) => setRequestDescription(e.target.value)}
//               rows={8}
//             />
//           </div>
          
//           <Button 
//             onClick={handleSubmitRequest} 
//             className="w-full" 
//             disabled={isSubmitting}
//           >
//             {isSubmitting ? (
//               <>
//                 <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
//                 Submitting...
//               </>
//             ) : (
//               <>
//                 <Send className="mr-2 h-4 w-4" />
//                 Submit Request
//               </>
//             )}
//           </Button>
//         </CardContent>
//       </Card>

//       {/* Request Templates */}
//       <Card>
//         <CardHeader>
//           <CardTitle>Request Templates</CardTitle>
//           <CardDescription>Use pre-defined templates to speed up your request process</CardDescription>
//         </CardHeader>
//         <CardContent>
//           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
//             {requestTemplates.map((template, index) => {
//               const IconComponent = template.icon;
//               return (
//                 <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary/20">
//                   <CardHeader className="pb-3">
//                     <div className="flex items-center gap-2">
//                       <IconComponent className={`h-5 w-5 ${template.color}`} />
//                       <CardTitle className="text-lg">{template.title}</CardTitle>
//                     </div>
//                     <CardDescription className="text-sm">{template.description}</CardDescription>
//                   </CardHeader>
//                   <CardContent className="pt-0">
//                     <Button 
//                       variant="outline" 
//                       className="w-full"
//                       onClick={() => handleUseTemplate(
//                         template.type, 
//                         template.templateTitle, 
//                         template.templateDescription
//                       )}
//                     >
//                       <Plus className="mr-2 h-4 w-4" />
//                       Use Template
//                     </Button>
//                   </CardContent>
//                 </Card>
//               );
//             })}
//           </div>
//         </CardContent>
//       </Card>
//     </div>
//   );
// };

export default ApplyRequest;
