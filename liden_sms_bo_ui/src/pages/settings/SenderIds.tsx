import { useState, useEffect } from "react"
import { <PERSON>, Plus, <PERSON>ting<PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON> } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { LoadingSpinner } from "@/components/LoadingSpinner"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { useDateRangePickerWithQuickSelect } from "@/hooks/useDateRangePicker"
import { LidenAPI } from "@/lib/api-index"

interface SenderId {
  total_count: string
  id: string
  sender_id: string
  sender_type: string
  sStatus: string
  short_code: string
  status: string
  client_id: string
  client_name: string
  client_email: string
}

interface SenderIdsResponse {
  total_count: string
  data: SenderId[]
}

export default function SenderIds() {
  const [senderIds, setSenderIds] = useState<SenderId[]>([])
  const [loading, setLoading] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("true")

  // Date picker hook
  const datePicker = useDateRangePickerWithQuickSelect({
    onDateRangeChange: () => {
      setCurrentPage(1) // Reset to first page when date range changes
    }
  })

  const fetchSenderIds = async () => {
    try {
      setLoading(true)
      const dateParams = datePicker.getAPIDateParams()
      const params = {
        limit: pageSize,
        offset: (currentPage - 1) * pageSize,
        typeId: typeFilter && typeFilter !== "all" ? typeFilter : "3,4", // Default to alphanumeric and shortcode
        status: statusFilter === "true" ? true : statusFilter === "false" ? false : undefined,
        shortCode: searchTerm || undefined,
        ...dateParams, // Include start and end date parameters
      }

      const response = await LidenAPI.client.getSenderIds(params)
      
      if (response.code === "Success" && response.data?.data) {
        const senderData = response.data.data as SenderIdsResponse
        setSenderIds(senderData.data || [])
        setTotalCount(parseInt(senderData.total_count || "0"))
      }
    } catch (error) {
      console.error("Failed to fetch sender IDs:", error)
      setSenderIds([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSenderIds()
  }, [currentPage, pageSize, typeFilter, statusFilter, datePicker.dateRange])

  const handleSearch = () => {
    setCurrentPage(1)
    fetchSenderIds()
  }

  const handleRefresh = () => {
    fetchSenderIds()
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string; variant: "default" | "destructive" | "secondary" | "outline"; className?: string }> = {
      "4": { label: "Active", variant: "default", className: "bg-green-500" },
      "3": { label: "Pending", variant: "secondary" },
      "2": { label: "Rejected", variant: "destructive" },
      "1": { label: "Draft", variant: "outline" },
    }
    
    const statusInfo = statusMap[status] || { label: `Status ${status}`, variant: "outline" as const }
    return (
      <Badge variant={statusInfo.variant} className={statusInfo.className}>
        {statusInfo.label}
      </Badge>
    )
  }

  const getTypeBadge = (type: string) => {
    const typeColors: Record<string, string> = {
      'ALPHANUMERIC': 'bg-blue-600',
      'SHORTCODE': 'bg-purple-600',
      'NUMERIC': 'bg-orange-600'
    }
    
    return (
      <Badge 
        variant="outline" 
        className={`${typeColors[type] || 'bg-gray-600'} text-white border-0`}
      >
        {type}
      </Badge>
    )
  }

  const totalPages = Math.ceil(totalCount / pageSize)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>Settings</span>
          <span>›</span>
          <span className="text-foreground">Sender IDs</span>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Request Sender ID
        </Button>
      </div>

      {/* Filters Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Sender ID Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Date Range Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Date Range</label>
            <DatePickerWithRange
              date={datePicker.dateRange}
              onDateChange={datePicker.setDateRange}
              placeholder="Select date range"
              className="w-full"
            />
          </div>

          {/* Other Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Per page</label>
              <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Type</label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="3,4">Alphanumeric & Shortcode</SelectItem>
                  <SelectItem value="3">Alphanumeric</SelectItem>
                  <SelectItem value="4">Shortcode</SelectItem>
                  <SelectItem value="2">Numeric</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search sender IDs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
                <Button onClick={handleSearch} variant="default">
                  Search
                </Button>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end">
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sender IDs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Sender IDs ({totalCount.toLocaleString()})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[80px]">ID</TableHead>
                      <TableHead>Sender ID</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Short Code</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Client</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {senderIds.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                          No sender IDs found
                        </TableCell>
                      </TableRow>
                    ) : (
                      senderIds.map((senderId) => (
                        <TableRow key={senderId.id}>
                          <TableCell className="font-mono text-sm">
                            {senderId.id}
                          </TableCell>
                          <TableCell className="font-medium">
                            {senderId.sender_id}
                          </TableCell>
                          <TableCell>
                            {getTypeBadge(senderId.sender_type)}
                          </TableCell>
                          <TableCell className="font-mono">
                            {senderId.short_code}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(senderId.status)}
                          </TableCell>
                          <TableCell className="text-sm">
                            {senderId.client_name}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {senderId.client_email}
                          </TableCell>
                          <TableCell>
                            <Button size="sm" variant="outline">
                              <Settings className="h-3 w-3" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} entries
                  </div>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                      
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1
                        return (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      })}
                      
                      <PaginationItem>
                        <PaginationNext 
                          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
