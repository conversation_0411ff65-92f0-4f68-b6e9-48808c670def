import { useNavigate, useParams } from "react-router-dom";
import CampaignDetailHeader from "@/components/CampaignDetailHeader";
import DeliveryStatsDashboard from "@/components/DeliveryStatsDashboard";
import StatusBreakdownTable from "@/components/StatusBreakdownTable";
import MessageDetailsTable from "@/components/MessageDetailsTable";

// Sample data - in a real app this would come from an API
const sampleCampaignData = {
  "235132": {
    id: "235132",
    status: "sent",
    stats: {
      totalSent: 9223,
      delivered: 2665,
      pending: 6,
      failed: 6552
    },
    statusBreakdown: [
      { status: "DeliveredToTerminal", count: 2665 },
      { status: "SenderName Blacklisted", count: 3208 },
      { status: "DeliveredToTerminal", count: 4 },
      { status: "AbsentSubscriber", count: 2194 },
      { status: "DeliveryImpossible", count: 146 }
    ],
    message: "1st deposit ya aku aku na FREE BONUS! DEPOSIT 450ksh+ UPATE SAI! LIVERPOOL v ARSENAL BRIGHTON v MANCITY mossbets.com STOP *456*9#"
  }
};

export default function CampaignDetail() {
  const navigate = useNavigate();
  const { campaignId } = useParams<{ campaignId: string }>();
  
  // Get campaign data (in real app, this would be fetched from API)
  const campaignData = campaignId ? sampleCampaignData[campaignId as keyof typeof sampleCampaignData] : null;

  if (!campaignData) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Campaign Not Found</h1>
          <p className="text-gray-400 mb-6">The campaign you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate(-1)}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const handleBack = () => {
    navigate(-1);
  };

  const handleExport = () => {
    console.log("Exporting campaign data...");
    // Implement export functionality
  };

  const handleAction = (action: string) => {
    console.log("Action:", action);
    // Implement action handlers
    switch (action) {
      case 'resend':
        // Handle resend
        break;
      case 'duplicate':
        // Handle duplicate
        break;
      case 'edit':
        // Handle edit
        break;
      case 'delete':
        // Handle delete
        break;
    }
  };

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <CampaignDetailHeader
        campaignId={campaignData.id}
        status={campaignData.status}
        onBack={handleBack}
        onExport={handleExport}
        onAction={handleAction}
      />

      {/* Main Content */}
      <div className="p-6 space-y-6">
        {/* Delivery Statistics Dashboard */}
        <DeliveryStatsDashboard
          stats={campaignData.stats}
          campaignMessage={campaignData.message}
        />

        {/* Status Breakdown and Message Details */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          <StatusBreakdownTable
            data={campaignData.statusBreakdown}
            totalCount={campaignData.stats.totalSent}
          />
          <div className="xl:col-span-1">
            <MessageDetailsTable />
          </div>
        </div>
      </div>
    </div>
  );
}
