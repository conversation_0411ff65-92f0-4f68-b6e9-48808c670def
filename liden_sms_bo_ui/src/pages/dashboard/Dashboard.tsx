import { useState } from "react"
import { Bar<PERSON>hart3, Users, MessageSquare } from "lucide-react"
import { StatCard } from "@/components/StatCard"
import { MetricCard } from "@/components/MetricCard"
import { DeliveryStats } from "@/components/DeliveryStats"
import { ComprehensiveDashboard } from "@/components/ComprehensiveDashboard"
import { ImprovedDashboardCards } from "@/components/ImprovedDashboardCards"
import { ImprovedDeliveryStats } from "@/components/ImprovedDeliveryStats"
import { ImprovedCharts } from "@/components/ImprovedCharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useDashboardData } from "@/hooks/useDashboardData"

export default function Dashboard() {
  const [viewMode, setViewMode] = useState<'legacy' | 'comprehensive' | 'improved'>('comprehensive')

  // Get dashboard data for legacy view
  const { data: { wallet, dashboardStats } } = useDashboardData(
    { clientId: "46" },
    { autoFetch: true }
  )







  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>Home</span>
          <span>›</span>
          <span className="text-foreground">Dashboard</span>
        </div>

        {/* <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'legacy' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('legacy')}
          >
            Legacy View
          </Button>
          <Button
            variant={viewMode === 'comprehensive' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('comprehensive')}
          >
            Enhanced View (Recommended)
          </Button>
          <Button
            variant={viewMode === 'improved' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('improved')}
          >
            Improved View
          </Button>
        </div> */}
      </div>

      {/* Dashboard Content */}
      {viewMode === 'comprehensive' ? (
        <ComprehensiveDashboard clientId="46" />
      ) : viewMode === 'improved' ? (
        <div className="space-y-6">
          <ImprovedDashboardCards clientId="46" />
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            <ImprovedDeliveryStats clientId="46" className="xl:col-span-1" />
            <div className="xl:col-span-2">
              <ImprovedCharts clientId="46" />
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Main Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Account Balance */}
            <StatCard
              title="Account Balance"
              value={wallet ? `${wallet.data.data.currency} ${parseFloat(wallet.data.data.balance).toLocaleString()}` : "Loading..."}
              subtitle={wallet ? `${wallet.data.data.currency} ${parseFloat(wallet.data.data.alert_threshold).toLocaleString()}` : ""}
              bonus={wallet ? `${wallet.data.data.currency} ${parseFloat(wallet.data.data.bonus).toLocaleString()}` : ""}
              icon={BarChart3}
              iconClassName="bg-blue-500"
              actions={[
                { label: "View SMS Units", variant: "outline" },
                { label: "Edit Threshold", variant: "default" }
              ]}
            />

            {/* Total Contacts */}
            <MetricCard
              title="Total Contacts"
              value={dashboardStats ? dashboardStats.data.data.contacts : "0"}
              subtitle=""
              bonus=""
              icon={Users}
              iconColor="text-red-500"
            />

            {/* Total Messages Sent */}
            <MetricCard
              title="Total Message Sent"
              value={dashboardStats ? dashboardStats.data.data.sms_sent : "0"}
              subtitle=""
              bonus=""
              icon={MessageSquare}
              iconColor="text-orange-500"
            />
          </div>

          {/* Second Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Delivery Stats */}
            <DeliveryStats />

            {/* Chart Placeholder */}
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-foreground">Delivery Chart</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-green-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-medium">Chart Area</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  )
}