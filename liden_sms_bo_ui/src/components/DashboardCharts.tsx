import {
  <PERSON><PERSON>hart,
  <PERSON>,
  Cell,
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts"
import { TrendingUp, <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon, BarChart3, RefreshCw, AlertCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { useDashboardData } from "@/hooks/useDashboardData"

interface DashboardChartsProps {
  clientId?: string
  className?: string
  dateRange?: {
    start: string
    end: string
  }
  onRefresh?: () => void
}

export function DashboardCharts({
  clientId,
  className,
  dateRange,
  onRefresh
}: DashboardChartsProps) {
  const {
    data: { dashboardStats: chartsData, wallet: walletData },
    refreshData,
    isLoading: loading,
    error
  } = useDashboardData({ clientId, dateRange })

  const refreshing = loading

  const handleRefresh = async () => {
    await refreshData()
    onRefresh?.()
  }

  // Prepare data for charts
  const deliveryPieData = chartsData ? [
    { name: 'Delivered', value: parseInt(chartsData.data.data.delivery_report.sms_delivered), color: '#10B981' },
    { name: 'Failed', value: parseInt(chartsData.data.data.delivery_report.sms_failed), color: '#EF4444' },
    { name: 'Pending', value: parseInt(chartsData.data.data.delivery_report.sms_pending), color: '#F59E0B' },
  ] : []

  // Since the new API doesn't provide daily stats, we'll create a simple representation
  const dailyTrendsData = chartsData ? [
    {
      date: 'Current Period',
      sent: parseInt(chartsData.data.data.sms_sent),
      delivered: parseInt(chartsData.data.data.delivery_report.sms_delivered),
      failed: parseInt(chartsData.data.data.delivery_report.sms_failed),
      pending: parseInt(chartsData.data.data.delivery_report.sms_pending),
    }
  ] : []

  const costAnalysisData = [] // Not available in new API

  const balanceData = walletData ? [
    { name: 'Current Balance', value: Math.abs(parseFloat(walletData.data.data.balance)), color: parseFloat(walletData.data.data.balance) < 0 ? '#EF4444' : '#3B82F6' },
    { name: 'Alert Threshold', value: parseFloat(walletData.data.data.alert_threshold), color: '#E5E7EB' },
  ] : []

  const COLORS = ['#10B981', '#EF4444', '#F59E0B', '#3B82F6', '#8B5CF6']

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <Card className={cn("bg-card border-border", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-64 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn("bg-card border-border border-red-200", className)}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm font-medium">Error loading chart data</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            className="mt-3"
            disabled={refreshing}
          >
            {refreshing ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("bg-card border-border transition-all duration-200 hover:shadow-lg", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            <span>Analytics Charts</span>
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="delivery" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="delivery">Delivery</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="costs">Costs</TabsTrigger>
            <TabsTrigger value="balance">Balance</TabsTrigger>
          </TabsList>
          
          <TabsContent value="delivery" className="mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground flex items-center space-x-2">
                <PieChartIcon className="h-4 w-4" />
                <span>Delivery Success Rate</span>
              </h4>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={deliveryPieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {deliveryPieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="trends" className="mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground flex items-center space-x-2">
                <TrendingUp className="h-4 w-4" />
                <span>SMS Usage Trends Over Time</span>
              </h4>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={dailyTrendsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Line type="monotone" dataKey="sent" stroke="#3B82F6" strokeWidth={2} name="Sent" />
                    <Line type="monotone" dataKey="delivered" stroke="#10B981" strokeWidth={2} name="Delivered" />
                    <Line type="monotone" dataKey="failed" stroke="#EF4444" strokeWidth={2} name="Failed" />
                    <Line type="monotone" dataKey="pending" stroke="#F59E0B" strokeWidth={2} name="Pending" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="costs" className="mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground">Cost Analysis</h4>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={costAnalysisData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar yAxisId="left" dataKey="cost" fill="#8B5CF6" name="Total Cost" />
                    <Line yAxisId="right" type="monotone" dataKey="costPerMessage" stroke="#F59E0B" strokeWidth={2} name="Cost per Message" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="balance" className="mt-4">
            {walletData ? (
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-muted-foreground">Balance vs Threshold</h4>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={balanceData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: ${value.toLocaleString()}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {balanceData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="text-center text-sm text-muted-foreground">
                  Current balance: {parseFloat(walletData.data.data.balance).toLocaleString()} {walletData.data.data.currency} |
                  Alert threshold: {parseFloat(walletData.data.data.alert_threshold).toLocaleString()} {walletData.data.data.currency}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <PieChartIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No wallet data available</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
