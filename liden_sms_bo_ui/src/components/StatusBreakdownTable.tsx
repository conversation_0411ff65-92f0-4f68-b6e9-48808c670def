import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface StatusBreakdown {
  status: string;
  count: number;
  percentage?: number;
}

interface StatusBreakdownTableProps {
  data: StatusBreakdown[];
  totalCount: number;
}

const defaultStatusData: StatusBreakdown[] = [
  { status: "DeliveredToTerminal", count: 2665 },
  { status: "SenderName Blacklisted", count: 3208 },
  { status: "DeliveredToTerminal", count: 4 },
  { status: "AbsentSubscriber", count: 2194 },
  { status: "DeliveryImpossible", count: 146 }
];

export default function StatusBreakdownTable({ 
  data = defaultStatusData, 
  totalCount 
}: StatusBreakdownTableProps) {
  // Calculate percentages if not provided
  const dataWithPercentages = data.map(item => ({
    ...item,
    percentage: item.percentage || (totalCount > 0 ? (item.count / totalCount) * 100 : 0)
  }));

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'deliveredtoterminal':
        return 'text-green-400';
      case 'sendername blacklisted':
        return 'text-red-400';
      case 'absentsubscriber':
        return 'text-yellow-400';
      case 'deliveryimpossible':
        return 'text-orange-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white text-lg">Status Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-slate-700 hover:bg-slate-750">
                <TableHead className="text-gray-300 font-medium">Status</TableHead>
                <TableHead className="text-gray-300 font-medium text-right">Total</TableHead>
                <TableHead className="text-gray-300 font-medium text-right">Percentage</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dataWithPercentages.map((item, index) => (
                <TableRow 
                  key={index} 
                  className="border-slate-700 hover:bg-slate-750/50"
                >
                  <TableCell className={`font-medium ${getStatusColor(item.status)}`}>
                    {item.status}
                  </TableCell>
                  <TableCell className="text-white text-right font-mono">
                    {item.count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-gray-300 text-right">
                    {item.percentage.toFixed(1)}%
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        
        {/* Summary */}
        <div className="mt-4 pt-4 border-t border-slate-700">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-400">Total Messages:</span>
            <span className="text-white font-mono font-medium">
              {totalCount.toLocaleString()}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
