import { Wallet, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>ren<PERSON>Up, TrendingDown, RefreshCw } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import { useWalletData } from "@/hooks/useDashboardData"

interface WalletOverviewCardProps {
  clientId?: string
  className?: string
  onRefresh?: () => void
}

export function WalletOverviewCard({
  clientId,
  className,
  onRefresh
}: WalletOverviewCardProps) {
  const {
    wallet: walletData,
    refreshWallet,
    isLoading: loading,
    error
  } = useWalletData(clientId)

  const refreshing = loading

  const handleRefresh = async () => {
    await refreshWallet()
    onRefresh?.()
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'KES',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const getBalanceStatus = () => {
    if (!walletData?.data?.data) return { status: 'normal', color: 'bg-green-500' }

    const balance = parseFloat(walletData.data.data.balance)
    const threshold = parseFloat(walletData.data.data.alert_threshold)
    const balancePercentage = (balance / threshold) * 100

    if (balancePercentage <= 25 || balance < 0) {
      return { status: 'critical', color: 'bg-red-500', textColor: 'text-red-600' }
    } else if (balancePercentage <= 50) {
      return { status: 'warning', color: 'bg-yellow-500', textColor: 'text-yellow-600' }
    } else {
      return { status: 'normal', color: 'bg-green-500', textColor: 'text-green-600' }
    }
  }

  const getThresholdProgress = () => {
    if (!walletData?.data?.data) return 0
    const balance = parseFloat(walletData.data.data.balance)
    const threshold = parseFloat(walletData.data.data.alert_threshold)
    return Math.min((balance / threshold) * 100, 100)
  }

  if (loading) {
    return (
      <Card className={cn("bg-card border-border", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-2 w-full" />
          <div className="flex justify-between">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-24" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn("bg-card border-border border-red-200", className)}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span className="text-sm font-medium">Error loading wallet data</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            className="mt-3"
            disabled={refreshing}
          >
            {refreshing ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!walletData) {
    return null
  }

  const balanceStatus = getBalanceStatus()
  const thresholdProgress = getThresholdProgress()

  return (
    <Card className={cn("bg-card border-border transition-all duration-200 hover:shadow-lg", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <Wallet className="h-5 w-5 text-primary" />
            <span>Wallet Overview</span>
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Client Info */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">Client</p>
            <p className="font-medium">{walletData.data.data.client_name}</p>
            <p className="text-xs text-muted-foreground">ID: {walletData.data.data.client_id}</p>
          </div>
          <div className="text-right">
            <Badge
              variant={walletData.data.data.status === "1" ? "default" : "secondary"}
              className={walletData.data.data.status === "1" ? "bg-green-100 text-green-800" : ""}
            >
              {walletData.data.data.status === "1" ? "Active" : "Inactive"}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">{walletData.data.data.country}</p>
          </div>
        </div>

        {/* Balance Display */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Current Balance</span>
            <div className="flex items-center space-x-1">
              {balanceStatus.status === 'critical' && <TrendingDown className="h-4 w-4 text-red-500" />}
              {balanceStatus.status === 'warning' && <AlertTriangle className="h-4 w-4 text-yellow-500" />}
              {balanceStatus.status === 'normal' && <TrendingUp className="h-4 w-4 text-green-500" />}
            </div>
          </div>
          <div className={cn("text-3xl font-bold", balanceStatus.textColor)}>
            {formatCurrency(parseFloat(walletData.data.data.balance), walletData.data.data.currency)}
          </div>
        </div>

        {/* Threshold Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Alert Threshold</span>
            <span className="font-medium">
              {formatCurrency(parseFloat(walletData.data.data.alert_threshold), walletData.data.data.currency)}
            </span>
          </div>
          <Progress 
            value={thresholdProgress} 
            className="h-2"
            // @ts-ignore - Custom progress color
            style={{ '--progress-background': balanceStatus.color } as any}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{thresholdProgress.toFixed(1)}% of threshold</span>
            <span>
              {walletData.balance > walletData.alertThreshold ? 'Above' : 'Below'} threshold
            </span>
          </div>
        </div>

        {/* Credit Limit (if available) */}
        {walletData.creditLimit && (
          <div className="pt-2 border-t border-border">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Credit Limit</span>
              <span className="font-medium">
                {formatCurrency(walletData.creditLimit, walletData.currency)}
              </span>
            </div>
          </div>
        )}

        {/* Last Updated */}
        <div className="pt-2 border-t border-border">
          <p className="text-xs text-muted-foreground">
            Last updated: {new Date(walletData.lastUpdated).toLocaleString()}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
