import { useState, useEffect } from "react";
import { Search, ChevronLeft, ChevronRight, RefreshCw, ArrowLeft, Eye, Users, MessageSquare, Clock, CheckCircle, XCircle, Download, TrendingUp, Calendar, FileText, FileSpreadsheet } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { apiService, LidenAPI } from "@/lib/api-index";
import { LoadingSpinner } from "@/components/LoadingSpinner";

// API Response interfaces
interface BulkUsageData {
  count: string;
  campaign_id: string;
  short_code: string;
  campaign_name: string;
  message: string;
  recipients: string;
  sent: string;
  delivered: string;
  failed: string;
  pending: string;
  delivery_percent: string;
  origin: string;
  cost_incurred: string;
  status: string;
  sms_pages: string;
  is_scheduled: string;
  send_time: string;
  completed_on: string;
  created_at: string;
  created_by: string;
  creator: string;
}

// Campaign Analytics interfaces
interface CampaignAnalytics {
  total_count: number;
  campaign_data: {
    message: string;
    recipients: string;
    origin: string;
    status: string;
    is_scheduled: string;
    short_code: string;
    created_at: string;
    completed_on: string;
    created_by: string;
  };
  analytic_data: Array<{
    total_count: string;
    network: string;
    delivery: string | null;
    date: string;
  }>;
  summary_data: Array<{
    outbox_id: string;
    msisdn: string;
    message: string;
    network: string;
    delivery: string;
    received_on: string;
    created_at: string;
  }>;
}

interface CampaignSummary {
  summary_analysis: Array<{
    campaign_id: string;
    name: string;
    recipients: string;
    short_code: string;
    sent: string;
    delivered: string;
    failed: string;
    pending: string;
    delivery_percentage: string;
    cost_incurred: string;
    cumulative_units: string;
    origin: string;
    status: string;
    send_time: string;
    created_by: string;
    client_name: string;
  }>;
  summary_analysis_desc: Array<{
    total_count: string;
    description: string;
  }>;
}

// BulkUsageResponse interface removed - using API service response format

interface SMSCampaign {
  id: string;
  sender: string;
  campaignId: string;
  message: string;
  date: string;
  status: 'sent' | 'pending' | 'scheduled' | 'failed';
  statusCode: boolean | number; // true for sent, 200 for pending, 700 for scheduled
  recipients?: number;
  stats?: {
    totalSent: number;
    delivered: number;
    pending: number;
    failed: number;
  };
}



// Component is now self-contained and doesn't need external props

export default function SMSCampaignTable(): JSX.Element {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState<'date' | 'sender' | 'campaignId'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedCampaigns, setSelectedCampaigns] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<'sent' | 'scheduled' | 'pending'>('sent');
  const [campaigns, setCampaigns] = useState<SMSCampaign[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  // Function to safely parse integer values
  const safeParseInt = (value: string | undefined | null, defaultValue: number = 0): number => {
    if (!value) return defaultValue;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  };

  // Function to format date safely
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      const now = new Date();
      const currentYear = now.getFullYear();
      const messageYear = date.getFullYear();

      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: messageYear !== currentYear ? 'numeric' : undefined
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Function to convert API data to SMSCampaign format
  const convertApiDataToCampaign = (apiData: BulkUsageData): SMSCampaign => {
    let status: 'sent' | 'pending' | 'scheduled' | 'failed';
    let statusCode: boolean | number;

    // Map status based on API status codes
    switch (apiData.status) {
      case '400': // Sent
        status = 'sent';
        statusCode = true;
        break;
      case '200': // Pending
        status = 'pending';
        statusCode = 200;
        break;
      case '700': // Scheduled
        status = 'scheduled';
        statusCode = 700;
        break;
      default:
        status = 'failed';
        statusCode = false;
    }

    // Safely parse numeric values
    const recipients = safeParseInt(apiData.recipients);
    const sent = safeParseInt(apiData.sent);
    const delivered = safeParseInt(apiData.delivered);
    const pending = safeParseInt(apiData.pending);
    const failed = safeParseInt(apiData.failed);

    return {
      id: apiData.campaign_id || 'unknown',
      sender: apiData.short_code || 'Unknown',
      campaignId: apiData.campaign_id || 'unknown',
      message: apiData.message || 'No message content',
      date: formatDate(apiData.send_time || apiData.created_at),
      status,
      statusCode,
      recipients,
      stats: {
        totalSent: sent,
        delivered,
        pending,
        failed
      }
    };
  };

  // Fetch campaigns from API using the authenticated API service
  const fetchCampaigns = async (status: string, retryCount: number = 0) => {
    setLoading(true);
    setError(null);

    try {
      // Calculate offset for pagination (API expects 1-based offset)
      const offset = ((currentPage - 1) * itemsPerPage) + 1;

      // Use the API service which automatically includes authentication headers
      const response = await apiService.get('/account/v1/view/bulk_usage', {
        limit: itemsPerPage.toString(),
        status: status,
        campaignId: searchQuery || '',
        offset: offset.toString(),
        start: '',
        end: ''
      });

      if (response.success && response.data?.data?.data?.data) {
        const campaigns = response.data.data.data.data;
        const totalCountStr = response.data.data.data.total_count;

        // Set total count for pagination
        setTotalCount(parseInt(totalCountStr || '0', 10));

        if (Array.isArray(campaigns)) {
          const convertedCampaigns = campaigns
            .filter(campaign => campaign && campaign.campaign_id) // Filter out invalid entries
            .map(convertApiDataToCampaign);
          setCampaigns(convertedCampaigns);
        } else {
          setCampaigns([]);
        }
      } else {
        setError(response.data?.statusDescription || 'Failed to fetch campaigns');
        setTotalCount(0);
      }
    } catch (err) {
      console.error('Error fetching campaigns:', err);

      if (err instanceof Error) {
        if (err.message.includes('Authentication token missing')) {
          setError('Please log in to view SMS campaigns');
        } else if (retryCount < 2) {
          // Retry up to 2 times
          setTimeout(() => fetchCampaigns(status, retryCount + 1), 1000 * (retryCount + 1));
          return;
        } else {
          setError(`Error: ${err.message}`);
        }
      } else {
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when tab, page, page size, or search changes
  useEffect(() => {
    const statusMap = {
      'sent': '400',
      'pending': '200',
      'scheduled': '700'
    };
    fetchCampaigns(statusMap[activeTab]);
  }, [activeTab, currentPage, itemsPerPage, searchQuery]);

  // Since we're using server-side pagination, campaigns are already filtered and paginated
  const paginatedCampaigns = campaigns;
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTabChange = (tab: 'sent' | 'scheduled' | 'pending') => {
    setActiveTab(tab);
    setCurrentPage(1); // Reset to first page when changing tabs
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newSize: number) => {
    setItemsPerPage(newSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const [selectedCampaign, setSelectedCampaign] = useState<SMSCampaign | null>(null);
  const [showCampaignDetails, setShowCampaignDetails] = useState(false);
  const [campaignAnalytics, setCampaignAnalytics] = useState<CampaignAnalytics | null>(null);
  const [campaignSummary, setCampaignSummary] = useState<CampaignSummary | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [searchMessages, setSearchMessages] = useState("");
  const [messagesPage, setMessagesPage] = useState(1);
  const messagesPerPage = 10;

  const fetchCampaignAnalytics = async (campaignId: string) => {
    try {
      setAnalyticsLoading(true);

      // Fetch both analytics and summary data
      const [analyticsResponse, summaryResponse] = await Promise.all([
        LidenAPI.sms.getCampaignAnalytics(campaignId),
        LidenAPI.sms.getCampaignSummary(campaignId)
      ]);

      if (analyticsResponse.success && analyticsResponse.data?.data?.data) {
        setCampaignAnalytics(analyticsResponse.data.data.data);
      }

      if (summaryResponse.success && summaryResponse.data?.data?.data) {
        setCampaignSummary(summaryResponse.data.data.data);
      }
    } catch (error) {
      console.error("Error fetching campaign analytics:", error);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const handleCampaignClick = (campaign: SMSCampaign) => {
    console.log('Campaign clicked:', campaign);
    setSelectedCampaign(campaign);
    setShowCampaignDetails(true);
    fetchCampaignAnalytics(campaign.campaignId);
  };

  const handleBackToList = () => {
    setShowCampaignDetails(false);
    setSelectedCampaign(null);
    setCampaignAnalytics(null);
    setCampaignSummary(null);
    setSearchMessages("");
    setMessagesPage(1);
  };

  // Download functions
  const downloadContactsCSV = () => {
    if (!analytics?.summary_data) return;

    const csvContent = [
      ['Phone', 'Network', 'Status', 'Date'],
      ...analytics.summary_data.map(msg => [
        msg.msisdn,
        msg.network,
        msg.delivery,
        new Date(msg.received_on).toLocaleString()
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `campaign_${selectedCampaign?.campaignId}_contacts.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const downloadDetailsPDF = () => {
    // For now, we'll use the browser's print functionality
    // In a real implementation, you'd use a library like jsPDF
    window.print();
  };

  // Campaign Details Component
  const renderCampaignDetails = () => {
    if (!selectedCampaign) return null;

    const summary = campaignSummary?.summary_analysis?.[0];
    const analytics = campaignAnalytics;

    // Get stats from summary data (sent, delivered, failed, pending)
    const totalSent = summary?.sent || selectedCampaign.recipients?.toString() || "0";
    const deliveredCount = summary?.delivered || "0";
    const failedCount = summary?.failed || "0";
    const pendingCount = summary?.pending || "0";

    // Calculate delivery percentage to 2 decimal places
    const deliveryPercentage = summary?.delivery_percentage ?
      parseFloat(summary.delivery_percentage).toFixed(2) :
      (parseInt(deliveredCount) / parseInt(totalSent) * 100).toFixed(2);

    // Filter messages based on search
    const filteredMessages = analytics?.summary_data?.filter(msg =>
      searchMessages === "" ||
      msg.msisdn.includes(searchMessages) ||
      msg.delivery.toLowerCase().includes(searchMessages.toLowerCase())
    ) || [];

    // Pagination for messages
    const totalMessages = filteredMessages.length;
    const totalPages = Math.ceil(totalMessages / messagesPerPage);
    const startIndex = (messagesPage - 1) * messagesPerPage;
    const endIndex = startIndex + messagesPerPage;
    const paginatedMessages = filteredMessages.slice(startIndex, endIndex);

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between bg-slate-800 p-4 rounded-lg">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={handleBackToList}
              className="text-white hover:bg-slate-700"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
            </Button>
            <div>
              <h2 className="text-lg font-bold text-white">Campaign ID {selectedCampaign.campaignId}</h2>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="text-white border-slate-600 hover:bg-slate-700"
              onClick={downloadContactsCSV}
              disabled={!analytics?.summary_data?.length}
            >
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="text-white border-slate-600 hover:bg-slate-700"
              onClick={downloadDetailsPDF}
            >
              <FileText className="h-4 w-4 mr-2" />
              PDF
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="bg-red-600 text-white border-red-600 hover:bg-red-700"
              onClick={() => fetchCampaignAnalytics(selectedCampaign.campaignId)}
              disabled={analyticsLoading}
            >
              {analyticsLoading ? <LoadingSpinner /> : "Action"}
            </Button>
          </div>
        </div>

        {analyticsLoading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <>
            {/* Summary Stats Cards at Top */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="bg-slate-800 border-slate-600">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-slate-400">Sent</p>
                      <p className="text-2xl font-bold text-white">{totalSent}</p>
                    </div>
                    <MessageSquare className="h-8 w-8 text-blue-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-600">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-slate-400">Delivered</p>
                      <p className="text-2xl font-bold text-green-400">{deliveredCount}</p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-600">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-slate-400">Failed</p>
                      <p className="text-2xl font-bold text-red-400">{failedCount}</p>
                    </div>
                    <XCircle className="h-8 w-8 text-red-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-600">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-slate-400">Pending</p>
                      <p className="text-2xl font-bold text-yellow-400">{pendingCount}</p>
                    </div>
                    <Clock className="h-8 w-8 text-yellow-400" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Compact Campaign Info */}
            <div className="bg-slate-800 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white">{summary?.short_code || selectedCampaign.sender}</h3>
                    <p className="text-sm text-slate-400">Campaign ID {selectedCampaign.campaignId}</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{deliveryPercentage}%</div>
                    <div className="text-xs text-slate-400">Delivery Rate</div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-slate-400">{summary?.send_time || selectedCampaign.date}</p>
                  <p className="text-sm text-slate-400">Cost: ${summary?.cost_incurred || '0.00'}</p>
                </div>
              </div>
            </div>

            {/* Status Summary Table */}
            <Card className="bg-slate-800 border-slate-600">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="border-slate-600">
                      <TableHead className="text-slate-300">Status</TableHead>
                      <TableHead className="text-slate-300 text-right">Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {campaignSummary?.summary_analysis_desc?.map((item, index) => (
                      <TableRow key={index} className="border-slate-600">
                        <TableCell className="text-white">{item.description}</TableCell>
                        <TableCell className="text-white text-right">{item.total_count}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Compact Message Content */}
            <Card className="bg-slate-800 border-slate-600">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <MessageSquare className="h-5 w-5 text-slate-400 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-slate-300 mb-1">Message Content</h4>
                    <p className="text-sm text-white leading-relaxed">
                      {analytics?.campaign_data?.message || selectedCampaign.message}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Search Messages */}
            <div className="flex items-center justify-between">
              <div className="flex-1 max-w-md">
                <h3 className="text-lg font-semibold text-white mb-2">Message Details</h3>
                <div className="relative">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="Search phone numbers..."
                    value={searchMessages}
                    onChange={(e) => {
                      setSearchMessages(e.target.value);
                      setMessagesPage(1); // Reset to first page when searching
                    }}
                    className="pl-10 bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>
              <div className="text-sm text-slate-400">
                {totalMessages} total messages
              </div>
            </div>

            {/* Messages Table */}
            <Card className="bg-slate-800 border-slate-600">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="border-slate-600">
                      <TableHead className="text-slate-300">Phone</TableHead>
                      <TableHead className="text-slate-300">Network</TableHead>
                      <TableHead className="text-slate-300">Status</TableHead>
                      <TableHead className="text-slate-300">Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedMessages.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-8 text-slate-400">
                          {searchMessages ? "No messages found matching your search" : "No messages available"}
                        </TableCell>
                      </TableRow>
                    ) : (
                      paginatedMessages.map((message, index) => (
                        <TableRow key={index} className="border-slate-600">
                          <TableCell className="text-white">{message.msisdn}</TableCell>
                          <TableCell className="text-white">{message.network}</TableCell>
                          <TableCell>
                            <Badge
                              variant={message.delivery === "DeliveredToTerminal" ? "default" : "destructive"}
                              className={message.delivery === "DeliveredToTerminal" ? "bg-green-600" : "bg-red-600"}
                            >
                              {message.delivery}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-white">
                            {new Date(message.received_on).toLocaleString()}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between">
                <div className="text-sm text-slate-400">
                  Showing {startIndex + 1} to {Math.min(endIndex, totalMessages)} of {totalMessages} messages
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setMessagesPage(prev => Math.max(prev - 1, 1))}
                    disabled={messagesPage === 1}
                    className="text-white border-slate-600 hover:bg-slate-700"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                  <span className="text-sm text-slate-400">
                    Page {messagesPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setMessagesPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={messagesPage === totalPages}
                    className="text-white border-slate-600 hover:bg-slate-700"
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* Summary info when no pagination */}
            {totalPages <= 1 && totalMessages > 0 && (
              <div className="text-center text-slate-400">
                <p>Showing all {totalMessages} messages</p>
              </div>
            )}
          </>
        )}
      </div>
    );
  };

  const handleSelectCampaign = (campaignId: string, checked: boolean) => {
    if (checked) {
      setSelectedCampaigns(prev => [...prev, campaignId]);
    } else {
      setSelectedCampaigns(prev => prev.filter(id => id !== campaignId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCampaigns(paginatedCampaigns.map(c => c.campaignId));
    } else {
      setSelectedCampaigns([]);
    }
  };

  const isAllSelected = paginatedCampaigns.length > 0 &&
    paginatedCampaigns.every(c => selectedCampaigns.includes(c.campaignId));
  const isIndeterminate = selectedCampaigns.length > 0 && !isAllSelected;



  // Main container with tabs
  // Conditional rendering based on view state
  if (showCampaignDetails) {
    return (
      <div className="bg-slate-800 border border-slate-700 rounded-lg p-6">
        {renderCampaignDetails()}
      </div>
    );
  }

  return (
    <div className="bg-slate-800 border border-slate-700 rounded-lg">
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <div className="border-b border-slate-700">
          <TabsList className="grid w-full grid-cols-3 bg-transparent">
            <TabsTrigger
              value="sent"
              className="data-[state=active]:bg-slate-700 data-[state=active]:text-white text-gray-400"
            >
              Sent
            </TabsTrigger>
            <TabsTrigger
              value="scheduled"
              className="data-[state=active]:bg-slate-700 data-[state=active]:text-white text-gray-400"
            >
              Schedule
            </TabsTrigger>
            <TabsTrigger
              value="pending"
              className="data-[state=active]:bg-slate-700 data-[state=active]:text-white text-gray-400"
            >
              Pending
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value={activeTab} className="p-6 space-y-4">
          <div className="space-y-4">
              {/* Search Bar */}
              <div className="flex gap-4 items-center">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search Campaign ID and press Enter"
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    const statusMap = {
                      'sent': '400',
                      'pending': '200',
                      'scheduled': '700'
                    };
                    fetchCampaigns(statusMap[activeTab]);
                  }}
                  disabled={loading}
                  className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600 disabled:opacity-50"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
                >
                  Select Date here
                </Button>
              </div>

      {/* Sort and Items per page controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex gap-2">
          <Select value={sortBy} onValueChange={(value: 'date' | 'sender' | 'campaignId') => setSortBy(value)}>
            <SelectTrigger className="w-40 bg-slate-800 border-slate-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              <SelectItem value="date" className="text-white">Sort by Date</SelectItem>
              <SelectItem value="sender" className="text-white">Sort by Sender</SelectItem>
              <SelectItem value="campaignId" className="text-white">Sort by Campaign ID</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </Button>
        </div>
        <Select value={itemsPerPage.toString()} onValueChange={(value) => handlePageSizeChange(parseInt(value))}>
          <SelectTrigger className="w-32 bg-slate-800 border-slate-700 text-white">
            <SelectValue />
          </SelectTrigger>
          <SelectContent className="bg-slate-800 border-slate-700">
            <SelectItem value="5" className="text-white">5 per page</SelectItem>
            <SelectItem value="10" className="text-white">10 per page</SelectItem>
            <SelectItem value="20" className="text-white">20 per page</SelectItem>
            <SelectItem value="50" className="text-white">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Select All Checkbox */}
      {paginatedCampaigns.length > 0 && (
        <div className="flex items-center gap-2 px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg">
          <Checkbox
            checked={isAllSelected}
            onCheckedChange={handleSelectAll}
            className="border-slate-600 data-[state=checked]:bg-red-600 data-[state=checked]:border-red-600"
          />
          <span className="text-sm text-gray-300">
            Select All ({selectedCampaigns.length} selected)
          </span>
        </div>
      )}

      {/* Campaign List */}
      <div className="space-y-3">
        {loading ? (
          <div className="space-y-3">
            {/* Loading skeleton */}
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="bg-slate-800 border border-slate-700 rounded-lg p-4 animate-pulse">
                <div className="flex items-start gap-3">
                  <div className="w-4 h-4 bg-slate-600 rounded mt-1"></div>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-4 bg-slate-600 rounded w-48"></div>
                      <div className="h-5 bg-slate-600 rounded w-16"></div>
                    </div>
                    <div className="h-3 bg-slate-600 rounded w-full"></div>
                    <div className="h-3 bg-slate-600 rounded w-3/4"></div>
                    <div className="flex gap-4">
                      <div className="h-3 bg-slate-600 rounded w-20"></div>
                      <div className="h-3 bg-slate-600 rounded w-24"></div>
                      <div className="h-3 bg-slate-600 rounded w-28"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className="text-red-400 mb-4">{error}</div>
            <Button
              onClick={() => {
                const statusMap = {
                  'sent': '400',
                  'pending': '200',
                  'scheduled': '700'
                };
                fetchCampaigns(statusMap[activeTab]);
              }}
              variant="outline"
              className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
            >
              Try Again
            </Button>
          </div>
        ) : paginatedCampaigns.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            {activeTab === 'scheduled' ? 'No Schedule Message Found' :
             activeTab === 'pending' ? 'No Pending Messages Found' :
             'No Sent Messages Found'}
          </div>
        ) : (
          paginatedCampaigns.map((campaign) => (
            <div
              key={campaign.id}
              className="bg-slate-800 border border-slate-700 rounded-lg p-4 hover:bg-slate-700 transition-colors"
            >
              <div className="flex items-start gap-3">
                {/* Checkbox */}
                <Checkbox
                  checked={selectedCampaigns.includes(campaign.campaignId)}
                  onCheckedChange={(checked) => handleSelectCampaign(campaign.campaignId, checked as boolean)}
                  className="mt-1 border-slate-600 data-[state=checked]:bg-red-600 data-[state=checked]:border-red-600"
                />

                {/* Campaign Content */}
                <div
                  className="flex-1 min-w-0 cursor-pointer"
                  onClick={() => handleCampaignClick(campaign)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-red-400 font-medium text-sm">
                          Campaign ID# {campaign.campaignId} via {campaign.sender}
                        </span>
                        {/* Status Badge */}
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            campaign.status === 'sent' ? 'bg-green-600 text-white border-green-600' :
                            campaign.status === 'pending' ? 'bg-yellow-600 text-white border-yellow-600' :
                            campaign.status === 'scheduled' ? 'bg-blue-600 text-white border-blue-600' :
                            campaign.status === 'failed' ? 'bg-red-600 text-white border-red-600' :
                            'bg-gray-600 text-white border-gray-600'
                          }`}
                        >
                          {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-400 mb-2">
                        {campaign.message.length > 100
                          ? `${campaign.message.substring(0, 100)}...`
                          : campaign.message}
                      </div>
                      <div className="flex items-center gap-4 text-xs text-gray-400">
                        <span>{campaign.date}</span>
                        <span>Recipients: {campaign.recipients || 0}</span>
                        <span>Sent by: API_Liden</span>
                      </div>

                      {/* Delivery Stats - Show if available */}
                      {campaign.stats && (
                        <div className="flex items-center gap-4 text-xs mt-2">
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-green-400">Delivered: {campaign.stats.delivered}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            <span className="text-yellow-400">Pending: {campaign.stats.pending}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            <span className="text-red-400">Failed: {campaign.stats.failed}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between pt-4">
          <div className="text-sm text-gray-400">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount.toLocaleString()} results
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700 disabled:opacity-50"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum: number;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className={`w-8 h-8 p-0 ${
                      currentPage === pageNum
                        ? "bg-red-600 text-white"
                        : "bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                    }`}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700 disabled:opacity-50"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
