/**
 * Improved Dashboard Cards
 * 
 * Redesigned cards that match the original dashboard design with better
 * styling, colors, and layout for wallet balance, contacts, and messages.
 */

import { BarChart3, Users, MessageSquare, TrendingUp, TrendingDown, AlertTriangle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { useDashboardData } from "@/hooks/useDashboardData"

interface ImprovedDashboardCardsProps {
  clientId?: string
  className?: string
}

export function ImprovedDashboardCards({ 
  clientId, 
  className 
}: ImprovedDashboardCardsProps) {
  const { data: { wallet, dashboardStats }, isLoading } = useDashboardData(
    { clientId },
    { autoFetch: true }
  )

  const formatCurrency = (amount: number, currency: string = 'KES') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const formatNumber = (num: string | number) => {
    return parseInt(num.toString()).toLocaleString()
  }

  // Wallet data
  const balance = wallet ? parseFloat(wallet.data.data.balance) : 0
  const threshold = wallet ? parseFloat(wallet.data.data.alert_threshold) : 0
  const currency = wallet?.data.data.currency || 'KES'
  const clientName = wallet?.data.data.client_name || 'Loading...'
  
  // Dashboard stats
  const messagesSent = dashboardStats?.data.data.sms_sent || '0'
  const totalContacts = dashboardStats?.data.data.contacts || '0'
  const delivered = dashboardStats?.data.data.delivery_report.sms_delivered || '0'
  const failed = dashboardStats?.data.data.delivery_report.sms_failed || '0'

  // Determine balance status
  const isNegative = balance < 0
  const isLowBalance = balance < threshold && balance >= 0
  const balanceStatus = isNegative ? 'negative' : isLowBalance ? 'warning' : 'good'

  if (isLoading) {
    return (
      <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", className)}>
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", className)}>
      {/* Wallet Balance Card - Large and prominent */}
      <Card className={cn(
        "relative overflow-hidden border-2 transition-all duration-200 hover:shadow-lg",
        balanceStatus === 'negative' && "border-red-200 bg-red-50/50",
        balanceStatus === 'warning' && "border-yellow-200 bg-yellow-50/50",
        balanceStatus === 'good' && "border-green-200 bg-green-50/50"
      )}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className={cn(
                "p-2 rounded-lg",
                balanceStatus === 'negative' && "bg-red-100",
                balanceStatus === 'warning' && "bg-yellow-100",
                balanceStatus === 'good' && "bg-green-100"
              )}>
                <BarChart3 className={cn(
                  "h-5 w-5",
                  balanceStatus === 'negative' && "text-red-600",
                  balanceStatus === 'warning' && "text-yellow-600",
                  balanceStatus === 'good' && "text-green-600"
                )} />
              </div>
              <div>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Account Balance
                </CardTitle>
                <p className="text-xs text-muted-foreground">{clientName}</p>
              </div>
            </div>
            {balanceStatus === 'negative' && (
              <TrendingDown className="h-5 w-5 text-red-500" />
            )}
            {balanceStatus === 'warning' && (
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
            )}
            {balanceStatus === 'good' && (
              <TrendingUp className="h-5 w-5 text-green-500" />
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className={cn(
                "text-3xl font-bold",
                balanceStatus === 'negative' && "text-red-600",
                balanceStatus === 'warning' && "text-yellow-600",
                balanceStatus === 'good' && "text-green-600"
              )}>
                {formatCurrency(Math.abs(balance), currency)}
                {isNegative && <span className="text-red-500 ml-1">-</span>}
              </div>
              <p className="text-sm text-muted-foreground">
                Threshold: {formatCurrency(threshold, currency)}
              </p>
            </div>
            
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" className="flex-1">
                View SMS Units
              </Button>
              <Button size="sm" className="flex-1">
                Edit Threshold
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Total Contacts Card */}
      <Card className="relative overflow-hidden border transition-all duration-200 hover:shadow-lg bg-gradient-to-br from-blue-50 to-blue-100/50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="p-2 rounded-lg bg-blue-100">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Contacts
              </CardTitle>
            </div>
            <Badge variant="secondary" className="bg-blue-100 text-blue-700">
              Contacts
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-blue-600">
              {formatNumber(totalContacts)}
            </div>
            <p className="text-sm text-muted-foreground">
              Active contacts in database
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Total Messages Sent Card */}
      <Card className="relative overflow-hidden border transition-all duration-200 hover:shadow-lg bg-gradient-to-br from-purple-50 to-purple-100/50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="p-2 rounded-lg bg-purple-100">
                <MessageSquare className="h-5 w-5 text-purple-600" />
              </div>
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Messages Sent
              </CardTitle>
            </div>
            <Badge variant="secondary" className="bg-purple-100 text-purple-700">
              SMS
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-purple-600">
              {formatNumber(messagesSent)}
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-green-600">
                ✓ {formatNumber(delivered)} delivered
              </span>
              <span className="text-red-600">
                ✗ {formatNumber(failed)} failed
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ImprovedDashboardCards
