import React, { useEffect, useState, ErrorBoundary } from 'react'
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import type { ThemeProviderProps } from 'next-themes/dist/types'

interface CustomThemeProviderProps extends ThemeProviderProps {
  children: React.ReactNode
}

class ThemeErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(_: Error) {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Theme provider error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI without theme provider
      return this.props.children
    }

    return this.props.children
  }
}

export function ThemeProvider({ children, ...props }: CustomThemeProviderProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    // Return children without theme provider during SSR/initial render
    return <>{children}</>
  }

  return (
    <ThemeErrorBoundary>
      <NextThemesProvider {...props}>
        {children}
      </NextThemesProvider>
    </ThemeErrorBoundary>
  )
}
