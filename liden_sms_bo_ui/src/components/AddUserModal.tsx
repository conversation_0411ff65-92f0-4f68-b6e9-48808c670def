import { useState, useEffect } from "react"
import { X, ChevronDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { LidenAPI } from "@/lib/api-index"
import { UserRole, CreateUserRequest } from "@/lib/api-types"
import { LoadingSpinner } from "@/components/LoadingSpinner"

interface AddUserModalProps {
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

interface Country {
  code: string
  name: string
  currency: string
}

export function AddUserModal({ open, onClose, onSuccess }: AddUserModalProps) {
  const [loading, setLoading] = useState(false)
  const [userRoles, setUserRoles] = useState<UserRole[]>([])
  const [countries, setCountries] = useState<Country[]>([])
  const [formData, setFormData] = useState({
    first_name: "",
    middle_name: "",
    sur_name: "",
    email_address: "",
    msisdn: "",
    country_code: "254", // Default to Kenya
    role_id: "",
  })

  // Fetch user roles and countries
  useEffect(() => {
    if (open) {
      fetchUserRoles()
      fetchCountries()
    }
  }, [open])

  const fetchUserRoles = async () => {
    try {
      const response = await LidenAPI.userManagement.getUserRoles()
      if (response.success && response.data) {
        setUserRoles(response.data.data || [])
      }
    } catch (error) {
      console.error("Failed to fetch user roles:", error)
    }
  }

  const fetchCountries = async () => {
    try {
      const response = await LidenAPI.client.getCountries()
      if (response.success && response.data) {
        // Transform the countries data structure
        const countriesData: Country[] = []
        const data = response.data.data || response.data
        
        if (typeof data === 'object') {
          Object.entries(data).forEach(([code, info]) => {
            if (Array.isArray(info) && info.length >= 3) {
              countriesData.push({
                code: code,
                name: info[1],
                currency: info[2]
              })
            }
          })
        }
        
        setCountries(countriesData)
      }
    } catch (error) {
      console.error("Failed to fetch countries:", error)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.first_name || !formData.sur_name || !formData.email_address || 
        !formData.msisdn || !formData.role_id) {
      alert("Please fill in all required fields")
      return
    }

    try {
      setLoading(true)
      
      const userData: CreateUserRequest = {
        first_name: formData.first_name,
        middle_name: formData.middle_name || undefined,
        sur_name: formData.sur_name,
        email_address: formData.email_address,
        msisdn: formData.msisdn,
        country_code: formData.country_code,
        role_id: formData.role_id,
      }

      const response = await LidenAPI.userManagement.createUser(userData)
      
      if (response.success) {
        onSuccess()
        onClose()
        // Reset form
        setFormData({
          first_name: "",
          middle_name: "",
          sur_name: "",
          email_address: "",
          msisdn: "",
          country_code: "254",
          role_id: "",
        })
      } else {
        alert("Failed to create user. Please try again.")
      }
    } catch (error) {
      console.error("Failed to create user:", error)
      alert("Failed to create user. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const selectedCountry = countries.find(c => c.code === formData.country_code)

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] bg-slate-800 text-white border-slate-700">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-xl font-semibold">Add New User</DialogTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0 text-gray-400 hover:text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* First Name */}
          <div className="space-y-2">
            <Label htmlFor="first_name" className="text-gray-300">
              First Name
            </Label>
            <Input
              id="first_name"
              value={formData.first_name}
              onChange={(e) => handleInputChange("first_name", e.target.value)}
              placeholder="John"
              className="bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
              required
            />
          </div>

          {/* Last Name */}
          <div className="space-y-2">
            <Label htmlFor="sur_name" className="text-gray-300">
              Last Name
            </Label>
            <Input
              id="sur_name"
              value={formData.sur_name}
              onChange={(e) => handleInputChange("sur_name", e.target.value)}
              placeholder="Doe"
              className="bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
              required
            />
          </div>

          {/* Middle Name */}
          <div className="space-y-2">
            <Label htmlFor="middle_name" className="text-gray-300">
              Middle Name
            </Label>
            <Input
              id="middle_name"
              value={formData.middle_name}
              onChange={(e) => handleInputChange("middle_name", e.target.value)}
              placeholder="Optional"
              className="bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
            />
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email_address" className="text-gray-300">
              Email
            </Label>
            <Input
              id="email_address"
              type="email"
              value={formData.email_address}
              onChange={(e) => handleInputChange("email_address", e.target.value)}
              placeholder="<EMAIL>"
              className="bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
              required
            />
          </div>

          {/* Phone Number */}
          <div className="space-y-2">
            <Label htmlFor="msisdn" className="text-gray-300">
              Phone Number
            </Label>
            <div className="flex">
              <Select value={formData.country_code} onValueChange={(value) => handleInputChange("country_code", value)}>
                <SelectTrigger className="w-[120px] bg-slate-700 border-slate-600 text-white">
                  <SelectValue>
                    {selectedCountry ? `${selectedCountry.name.slice(0, 2).toUpperCase()} (+${selectedCountry.code})` : `KE (+254)`}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  {countries.map((country) => (
                    <SelectItem key={country.code} value={country.code} className="text-white hover:bg-slate-600">
                      {country.name} (+{country.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                id="msisdn"
                value={formData.msisdn}
                onChange={(e) => handleInputChange("msisdn", e.target.value)}
                placeholder="725560980"
                className="flex-1 ml-2 bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
                required
              />
            </div>
          </div>

          {/* Role */}
          <div className="space-y-2">
            <Label htmlFor="role_id" className="text-gray-300">
              Role
            </Label>
            <Select value={formData.role_id} onValueChange={(value) => handleInputChange("role_id", value)}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Select a role" />
                <ChevronDown className="h-4 w-4 opacity-50" />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600">
                {userRoles.map((role) => (
                  <SelectItem key={role.role_id} value={role.role_id} className="text-white hover:bg-slate-600">
                    {role.role_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-slate-600 text-gray-300 hover:bg-slate-700"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {loading ? <LoadingSpinner /> : "Add"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
