/**
 * Improved Delivery Stats Component
 * 
 * Enhanced delivery statistics display that matches the original design
 * with better visual representation of SMS delivery data.
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { TrendingUp, MessageSquare, CheckCircle, XCircle, Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { useDashboardData } from "@/hooks/useDashboardData"

interface ImprovedDeliveryStatsProps {
  clientId?: string
  className?: string
}

export function ImprovedDeliveryStats({ 
  clientId, 
  className 
}: ImprovedDeliveryStatsProps) {
  const { data: { dashboardStats }, isLoading } = useDashboardData(
    { clientId },
    { autoFetch: true }
  )

  if (isLoading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/3"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 animate-pulse">
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!dashboardStats) {
    return (
      <Card className={cn("", className)}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No delivery data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const deliveryData = dashboardStats.data.data.delivery_report
  const sent = parseInt(deliveryData.sms_recipients)
  const delivered = parseInt(deliveryData.sms_delivered)
  const failed = parseInt(deliveryData.sms_failed)
  const pending = parseInt(deliveryData.sms_pending)

  const deliveryRate = sent > 0 ? (delivered / sent) * 100 : 0
  const failureRate = sent > 0 ? (failed / sent) * 100 : 0
  const pendingRate = sent > 0 ? (pending / sent) * 100 : 0

  const formatNumber = (num: number) => num.toLocaleString()
  const formatPercentage = (num: number) => `${num.toFixed(1)}%`

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span>SMS Delivery Statistics</span>
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Last 7 days delivery performance
            </p>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            {formatPercentage(deliveryRate)} Success
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Overall Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-2xl font-bold text-blue-600">
              {formatNumber(sent)}
            </div>
            <div className="text-xs text-blue-700 font-medium">Total Sent</div>
          </div>
          
          <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="text-2xl font-bold text-green-600">
              {formatNumber(delivered)}
            </div>
            <div className="text-xs text-green-700 font-medium">Delivered</div>
          </div>
          
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600">
              {formatNumber(failed)}
            </div>
            <div className="text-xs text-red-700 font-medium">Failed</div>
          </div>
          
          <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-2xl font-bold text-yellow-600">
              {formatNumber(pending)}
            </div>
            <div className="text-xs text-yellow-700 font-medium">Pending</div>
          </div>
        </div>

        {/* Progress Bars */}
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Delivered</span>
              </div>
              <span className="text-sm text-green-600 font-medium">
                {formatPercentage(deliveryRate)}
              </span>
            </div>
            <Progress 
              value={deliveryRate} 
              className="h-3 bg-green-100" 
              style={{ '--progress-background': '#10B981' } as any}
            />
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium">Failed</span>
              </div>
              <span className="text-sm text-red-600 font-medium">
                {formatPercentage(failureRate)}
              </span>
            </div>
            <Progress 
              value={failureRate} 
              className="h-3 bg-red-100" 
              style={{ '--progress-background': '#EF4444' } as any}
            />
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium">Pending</span>
              </div>
              <span className="text-sm text-yellow-600 font-medium">
                {formatPercentage(pendingRate)}
              </span>
            </div>
            <Progress 
              value={pendingRate} 
              className="h-3 bg-yellow-100" 
              style={{ '--progress-background': '#F59E0B' } as any}
            />
          </div>
        </div>

        {/* Action Button */}
        <div className="pt-4 border-t">
          <Button variant="outline" className="w-full" size="sm">
            <MessageSquare className="h-4 w-4 mr-2" />
            View Detailed Report
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default ImprovedDeliveryStats
