import { useState, useMemo } from "react";
import { Search, ChevronLeft, ChevronRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface MessageDetail {
  id: string;
  phone: string;
  network: string;
  status: string;
  date: string;
  time: string;
}

const sampleMessageData: MessageDetail[] = [
  {
    id: "1",
    phone: "254704650312",
    network: "SAFARICOM",
    status: "DeliveredToTerminal",
    date: "2025-08-31",
    time: "07:18:11"
  },
  // Add more sample data to demonstrate pagination
  ...Array.from({ length: 50 }, (_, i) => ({
    id: (i + 2).toString(),
    phone: `25470465${String(i + 100).padStart(4, '0')}`,
    network: ["SAFARICOM", "AIRTEL", "TELKOM"][i % 3],
    status: ["DeliveredToTerminal", "SenderName Blacklisted", "AbsentSubscriber", "DeliveryImpossible"][i % 4],
    date: "2025-08-31",
    time: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`
  }))
];

interface MessageDetailsTableProps {
  data?: MessageDetail[];
}

export default function MessageDetailsTable({ data = sampleMessageData }: MessageDetailsTableProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const filteredData = useMemo(() => {
    if (searchQuery.trim() === "") {
      return data;
    }
    return data.filter(item =>
      item.phone.includes(searchQuery) ||
      item.network.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.status.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [data, searchQuery]);

  // For now, keep client-side pagination since this is a details table
  // In the future, this could be updated to use server-side pagination if needed
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'deliveredtoterminal':
        return 'bg-green-600 text-white';
      case 'sendername blacklisted':
        return 'bg-red-600 text-white';
      case 'absentsubscriber':
        return 'bg-yellow-600 text-white';
      case 'deliveryimpossible':
        return 'bg-orange-600 text-white';
      default:
        return 'bg-gray-600 text-white';
    }
  };

  const getNetworkColor = (network: string) => {
    switch (network.toLowerCase()) {
      case 'safaricom':
        return 'text-green-400';
      case 'airtel':
        return 'text-red-400';
      case 'telkom':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <CardTitle className="text-white text-lg">Message Details</CardTitle>
          <div className="flex gap-2 w-full sm:w-auto">
            <div className="relative flex-1 sm:flex-none">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search Mobile"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setCurrentPage(1);
                }}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-gray-400 w-full sm:w-64"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-slate-700 hover:bg-slate-750">
                <TableHead className="text-gray-300 font-medium">Phone</TableHead>
                <TableHead className="text-gray-300 font-medium">Network</TableHead>
                <TableHead className="text-gray-300 font-medium">Status</TableHead>
                <TableHead className="text-gray-300 font-medium">Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.map((item) => (
                <TableRow 
                  key={item.id} 
                  className="border-slate-700 hover:bg-slate-750/50"
                >
                  <TableCell className="text-white font-mono">
                    {item.phone}
                  </TableCell>
                  <TableCell className={`font-medium ${getNetworkColor(item.network)}`}>
                    {item.network}
                  </TableCell>
                  <TableCell>
                    <Badge className={`text-xs ${getStatusColor(item.status)}`}>
                      {item.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-gray-300">
                    {item.date} {item.time}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between pt-4 mt-4 border-t border-slate-700">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Show</span>
              <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(parseInt(value))}>
                <SelectTrigger className="w-20 bg-slate-700 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="10" className="text-white">10</SelectItem>
                  <SelectItem value="25" className="text-white">25</SelectItem>
                  <SelectItem value="50" className="text-white">50</SelectItem>
                  <SelectItem value="100" className="text-white">100</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-gray-400">entries</span>
            </div>
            
            <div className="text-sm text-gray-400">
              Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredData.length)} of {filteredData.length} entries
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600 disabled:opacity-50"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(pageNum)}
                      className={`w-8 h-8 p-0 ${
                        currentPage === pageNum 
                          ? "bg-red-600 text-white" 
                          : "bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
                      }`}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600 disabled:opacity-50"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
