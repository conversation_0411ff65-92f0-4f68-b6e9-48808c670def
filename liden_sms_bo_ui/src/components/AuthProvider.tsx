import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  User,
  AuthContextType,
  realLogin,
  logout,
  getStoredUser,
  isTokenValid
} from '@/lib/auth';
import { apiService } from '@/lib/api-index';
import { CookieManager } from '@/lib/utils';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedUser = getStoredUser();
        const tokenValid = isTokenValid();
        
        if (storedUser && tokenValid) {
          setUser(storedUser);
          // Set auth token in API service
          const token = CookieManager.getAuthToken();
          if (token) {
            apiService.setAuthToken(token);
          }

          // Migrate from localStorage if needed
          CookieManager.migrateFromLocalStorage();
        } else {
          // Clear invalid auth data
          logout();
          apiService.removeAuthToken();
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        logout();
        apiService.removeAuthToken();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Monitor token expiration
  useEffect(() => {
    if (!user) return;

    const checkTokenExpiration = () => {
      const { isExpired, timeRemaining } = CookieManager.getTokenExpirationInfo();

      if (isExpired) {
        console.warn('Token expired, logging out user');
        logout();
        return;
      }

      // Warn user when token is expiring soon (5 minutes)
      if (timeRemaining > 0 && timeRemaining < 5 * 60 * 1000) {
        console.warn('Token expiring soon:', Math.floor(timeRemaining / 60000), 'minutes remaining');
        // You could show a toast notification here
      }
    };

    // Check immediately
    checkTokenExpiration();

    // Check every minute
    const interval = setInterval(checkTokenExpiration, 60000);

    return () => clearInterval(interval);
  }, [user]);

  const login = async (phoneNumber: string, password: string, countryCode: string, _rememberMe?: boolean): Promise<boolean> => {
    setIsLoading(true);

    try {
      // Use real API login only
      const loggedInUser = await realLogin(phoneNumber, password, countryCode);

      if (loggedInUser) {
        setUser(loggedInUser);

        // Set auth token in API service
        const token = CookieManager.getAuthToken();
        if (token) {
          apiService.setAuthToken(token);
        }

        console.log('Login successful, token stored in secure cookies');
        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    setUser(null);
    logout();
    apiService.removeAuthToken();
    console.log('User logged out, all authentication data cleared');
  };

  const forgotPassword = async (_phoneNumber: string, _countryCode: string): Promise<boolean> => {
    // TODO: Implement real forgot password API call
    console.log('Forgot password not implemented yet');
    return false;
  };

  const resetPassword = async (_token: string, _newPassword: string): Promise<boolean> => {
    // TODO: Implement real reset password API call
    console.log('Reset password not implemented yet');
    return false;
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    login,
    logout: handleLogout,
    forgotPassword,
    resetPassword,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
