import { Loader2, Re<PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg"
  text?: string
  className?: string
}

export function LoadingSpinner({ 
  size = "md", 
  text = "Loading...", 
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", 
    lg: "h-8 w-8"
  }

  return (
    <div className={cn("flex items-center justify-center space-x-2", className)}>
      <Loader2 className={cn("animate-spin", sizeClasses[size])} />
      {text && <span className="text-sm text-muted-foreground">{text}</span>}
    </div>
  )
}

interface LoadingCardProps {
  title?: string
  className?: string
  rows?: number
}

export function LoadingCard({ 
  title = "Loading...", 
  className,
  rows = 3 
}: LoadingCardProps) {
  return (
    <Card className={cn("bg-card border-border", className)}>
      <CardContent className="p-6 space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
        {[...Array(rows)].map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-2 w-3/4" />
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

interface LoadingGridProps {
  columns?: number
  rows?: number
  className?: string
}

export function LoadingGrid({ 
  columns = 3, 
  rows = 2, 
  className 
}: LoadingGridProps) {
  return (
    <div className={cn(
      "grid gap-6",
      columns === 1 && "grid-cols-1",
      columns === 2 && "grid-cols-1 md:grid-cols-2",
      columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      columns === 4 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
      className
    )}>
      {[...Array(columns * rows)].map((_, i) => (
        <LoadingCard key={i} />
      ))}
    </div>
  )
}

interface RefreshButtonProps {
  onRefresh: () => void
  refreshing: boolean
  size?: "sm" | "md" | "lg"
  variant?: "ghost" | "outline" | "default"
  className?: string
}

export function RefreshButton({ 
  onRefresh, 
  refreshing, 
  size = "sm",
  variant = "ghost",
  className 
}: RefreshButtonProps) {
  return (
    <button
      onClick={onRefresh}
      disabled={refreshing}
      className={cn(
        "inline-flex items-center justify-center rounded-md font-medium transition-colors",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
        "disabled:pointer-events-none disabled:opacity-50",
        // Size variants
        size === "sm" && "h-8 w-8 p-0",
        size === "md" && "h-9 w-9 p-0", 
        size === "lg" && "h-10 w-10 p-0",
        // Style variants
        variant === "ghost" && "hover:bg-accent hover:text-accent-foreground",
        variant === "outline" && "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        variant === "default" && "bg-primary text-primary-foreground hover:bg-primary/90",
        className
      )}
    >
      <RefreshCw className={cn(
        "animate-spin" && refreshing,
        size === "sm" && "h-4 w-4",
        size === "md" && "h-5 w-5",
        size === "lg" && "h-6 w-6"
      )} />
    </button>
  )
}

// Responsive container for dashboard components
interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full"
}

export function ResponsiveContainer({ 
  children, 
  className,
  maxWidth = "full" 
}: ResponsiveContainerProps) {
  return (
    <div className={cn(
      "w-full mx-auto px-4 sm:px-6 lg:px-8",
      maxWidth === "sm" && "max-w-sm",
      maxWidth === "md" && "max-w-md", 
      maxWidth === "lg" && "max-w-lg",
      maxWidth === "xl" && "max-w-xl",
      maxWidth === "2xl" && "max-w-2xl",
      maxWidth === "full" && "max-w-full",
      className
    )}>
      {children}
    </div>
  )
}

// Mobile-friendly grid component
interface MobileGridProps {
  children: React.ReactNode
  columns?: {
    mobile?: number
    tablet?: number
    desktop?: number
  }
  gap?: number
  className?: string
}

export function MobileGrid({ 
  children, 
  columns = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 4,
  className 
}: MobileGridProps) {
  return (
    <div className={cn(
      "grid",
      `gap-${gap}`,
      `grid-cols-${columns.mobile || 1}`,
      `md:grid-cols-${columns.tablet || 2}`,
      `lg:grid-cols-${columns.desktop || 3}`,
      className
    )}>
      {children}
    </div>
  )
}
