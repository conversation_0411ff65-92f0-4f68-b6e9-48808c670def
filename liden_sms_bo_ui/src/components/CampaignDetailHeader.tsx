import { ArrowLeft, Download, MoreVertical } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface CampaignDetailHeaderProps {
  campaignId: string;
  status?: string;
  onBack?: () => void;
  onExport?: () => void;
  onAction?: (action: string) => void;
}

export default function CampaignDetailHeader({
  campaignId,
  status = "sent",
  onBack,
  onExport,
  onAction
}: CampaignDetailHeaderProps) {
  return (
    <div className="flex items-center justify-between p-4 bg-slate-800 border-b border-slate-700">
      {/* Left side - Back button and Campaign ID */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBack}
          className="text-gray-400 hover:text-white hover:bg-slate-700"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-lg font-semibold text-white">
            Campaign ID {campaignId}
          </h1>
          <Badge 
            variant="outline" 
            className="mt-1 text-xs border-slate-600 text-gray-300"
          >
            {status.toUpperCase()}
          </Badge>
        </div>
      </div>

      {/* Right side - Action buttons */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onExport}
          className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
        >
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="bg-red-600 border-red-600 text-white hover:bg-red-700"
            >
              Action
              <MoreVertical className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
            <DropdownMenuItem 
              className="text-white hover:bg-slate-700"
              onClick={() => onAction?.('resend')}
            >
              Resend Campaign
            </DropdownMenuItem>
            <DropdownMenuItem 
              className="text-white hover:bg-slate-700"
              onClick={() => onAction?.('duplicate')}
            >
              Duplicate Campaign
            </DropdownMenuItem>
            <DropdownMenuItem 
              className="text-white hover:bg-slate-700"
              onClick={() => onAction?.('edit')}
            >
              Edit Campaign
            </DropdownMenuItem>
            <DropdownMenuItem 
              className="text-red-400 hover:bg-slate-700"
              onClick={() => onAction?.('delete')}
            >
              Delete Campaign
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
