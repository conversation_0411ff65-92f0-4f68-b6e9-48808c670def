/**
 * Improved Charts Component
 * 
 * Enhanced charts and graphs that better visualize SMS delivery data
 * with proper colors, styling, and interactive elements.
 */

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from "recharts"
import { TrendingUp, Bar<PERSON>hart3, <PERSON><PERSON><PERSON> as PieChartIcon, Activity } from "lucide-react"
import { cn } from "@/lib/utils"
import { useDashboardData } from "@/hooks/useDashboardData"

interface ImprovedChartsProps {
  clientId?: string
  className?: string
}

const COLORS = {
  delivered: '#10B981', // Green
  failed: '#EF4444',    // Red
  pending: '#F59E0B',   // Yellow
  sent: '#3B82F6',      // Blue
}

export function ImprovedCharts({ 
  clientId, 
  className 
}: ImprovedChartsProps) {
  const { data: { dashboardStats, bulkUsage }, isLoading } = useDashboardData(
    { clientId },
    { autoFetch: true }
  )

  if (isLoading) {
    return (
      <div className={cn("grid grid-cols-1 lg:grid-cols-2 gap-6", className)}>
        {[1, 2].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!dashboardStats) {
    return (
      <div className={cn("text-center p-8", className)}>
        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
        <p className="text-muted-foreground">No chart data available</p>
      </div>
    )
  }

  const deliveryData = dashboardStats.data.data.delivery_report
  const sent = parseInt(deliveryData.sms_recipients)
  const delivered = parseInt(deliveryData.sms_delivered)
  const failed = parseInt(deliveryData.sms_failed)
  const pending = parseInt(deliveryData.sms_pending)

  // Pie chart data for delivery status
  const pieData = [
    { name: 'Delivered', value: delivered, color: COLORS.delivered },
    { name: 'Failed', value: failed, color: COLORS.failed },
    { name: 'Pending', value: pending, color: COLORS.pending },
  ]

  // Bar chart data for delivery comparison
  const barData = [
    {
      name: 'SMS Status',
      delivered,
      failed,
      pending,
      sent,
    }
  ]

  // Campaign data from bulk usage
  const campaignData = bulkUsage?.data?.data?.data?.slice(0, 5).map((campaign, index) => ({
    name: `Campaign ${index + 1}`,
    sent: parseInt(campaign.sent),
    delivered: parseInt(campaign.delivered),
    failed: parseInt(campaign.failed),
    rate: parseFloat(campaign.delivery_percent),
  })) || []

  const formatNumber = (num: number) => num.toLocaleString()
  const formatPercentage = (num: number) => `${num.toFixed(1)}%`

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.dataKey}: {formatNumber(entry.value)}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className={cn("grid grid-cols-1 lg:grid-cols-2 gap-6", className)}>
      {/* Delivery Status Pie Chart */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <PieChartIcon className="h-5 w-5 text-blue-600" />
              <CardTitle>Delivery Status Distribution</CardTitle>
            </div>
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              {formatNumber(sent)} Total
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number) => [formatNumber(value), '']}
                  labelFormatter={(label) => `${label} Messages`}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          
          {/* Legend */}
          <div className="flex justify-center space-x-4 mt-4">
            {pieData.map((entry, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-sm">
                  {entry.name}: {formatNumber(entry.value)}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Delivery Performance Bar Chart */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              <CardTitle>Delivery Performance</CardTitle>
            </div>
            <Badge variant="outline" className="bg-green-50 text-green-700">
              {formatPercentage((delivered / sent) * 100)} Success
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="delivered" fill={COLORS.delivered} name="Delivered" radius={[4, 4, 0, 0]} />
                <Bar dataKey="failed" fill={COLORS.failed} name="Failed" radius={[4, 4, 0, 0]} />
                <Bar dataKey="pending" fill={COLORS.pending} name="Pending" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
          
          {/* Performance Metrics */}
          <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">
                {formatPercentage((delivered / sent) * 100)}
              </div>
              <div className="text-xs text-muted-foreground">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-red-600">
                {formatPercentage((failed / sent) * 100)}
              </div>
              <div className="text-xs text-muted-foreground">Failure Rate</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-yellow-600">
                {formatPercentage((pending / sent) * 100)}
              </div>
              <div className="text-xs text-muted-foreground">Pending Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Campaign Performance (if available) */}
      {campaignData.length > 0 && (
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Activity className="h-5 w-5 text-purple-600" />
                <CardTitle>Recent Campaign Performance</CardTitle>
              </div>
              <Button variant="outline" size="sm">
                View All Campaigns
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={campaignData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Line 
                    type="monotone" 
                    dataKey="rate" 
                    stroke={COLORS.delivered} 
                    strokeWidth={3}
                    dot={{ fill: COLORS.delivered, strokeWidth: 2, r: 6 }}
                    name="Delivery Rate (%)"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default ImprovedCharts
