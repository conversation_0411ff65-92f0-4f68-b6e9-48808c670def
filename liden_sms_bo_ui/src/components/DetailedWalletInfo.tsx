/**
 * Detailed Wallet Information Component
 * 
 * Displays comprehensive wallet data including balance, thresholds,
 * account details, and financial status.
 */

import React from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Wallet, 
  TrendingDown, 
  TrendingUp, 
  AlertTriangle, 
  MapPin, 
  Mail, 
  Building, 
  CreditCard,
  Shield,
  Globe,
  Phone,
  DollarSign
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useWalletData } from "@/hooks/useDashboardData"

interface DetailedWalletInfoProps {
  clientId?: string
  className?: string
}

export function DetailedWalletInfo({ 
  clientId, 
  className
}: DetailedWalletInfoProps) {
  const { 
    wallet: walletData, 
    isLoading: loading, 
    error 
  } = useWalletData(clientId)

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/3"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 animate-pulse">
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !walletData) {
    return (
      <Card className={cn("", className)}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Wallet className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No wallet data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const data = walletData.data.data
  const balance = parseFloat(data.balance)
  const threshold = parseFloat(data.alert_threshold)
  const bonus = parseFloat(data.bonus)
  const subscriptionBalance = parseFloat(data.subscription_balance)
  const maxCredit = parseFloat(data.maximum_credit_amount)

  const isNegative = balance < 0
  const isLowBalance = balance < threshold && balance >= 0
  const balanceStatus = isNegative ? 'critical' : isLowBalance ? 'warning' : 'good'

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: data.currency,
      minimumFractionDigits: 2,
    }).format(Math.abs(amount))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'good': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'critical': return <TrendingDown className="h-5 w-5" />
      case 'warning': return <AlertTriangle className="h-5 w-5" />
      case 'good': return <TrendingUp className="h-5 w-5" />
      default: return <Wallet className="h-5 w-5" />
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'critical': return 'Critical - Negative Balance'
      case 'warning': return 'Warning - Below Threshold'
      case 'good': return 'Good - Above Threshold'
      default: return 'Unknown'
    }
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Wallet className="h-5 w-5 text-blue-600" />
              <span>Wallet Details</span>
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Account ID: {data.accountId} • Client ID: {data.client_id}
            </p>
          </div>
          <Badge variant="outline" className={cn("", getStatusColor(balanceStatus))}>
            {getStatusIcon(balanceStatus)}
            <span className="ml-1">{getStatusLabel(balanceStatus)}</span>
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Balance Alert */}
        {balanceStatus !== 'good' && (
          <Alert className={cn("", 
            balanceStatus === 'critical' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'
          )}>
            <AlertTriangle className={cn("h-4 w-4", 
              balanceStatus === 'critical' ? 'text-red-600' : 'text-yellow-600'
            )} />
            <AlertDescription className={cn("",
              balanceStatus === 'critical' ? 'text-red-700' : 'text-yellow-700'
            )}>
              {balanceStatus === 'critical' 
                ? `Your account has a negative balance of ${formatCurrency(balance)}. Please top up immediately.`
                : `Your balance is below the alert threshold of ${formatCurrency(threshold)}.`
              }
            </AlertDescription>
          </Alert>
        )}

        {/* Balance Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className={cn("p-4 rounded-lg border-2", getStatusColor(balanceStatus))}>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Current Balance</span>
              {isNegative && <span className="text-red-500 font-bold">NEGATIVE</span>}
            </div>
            <div className={cn("text-2xl font-bold", 
              balanceStatus === 'critical' ? 'text-red-600' : 
              balanceStatus === 'warning' ? 'text-yellow-600' : 'text-green-600'
            )}>
              {isNegative && '-'}{formatCurrency(balance)}
            </div>
          </div>

          <div className="p-4 rounded-lg border bg-blue-50 border-blue-200">
            <div className="text-sm font-medium text-blue-700 mb-2">Alert Threshold</div>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(threshold)}
            </div>
          </div>
        </div>

        {/* Additional Balances */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
            <div className="text-lg font-bold text-purple-600">
              {formatCurrency(bonus)}
            </div>
            <div className="text-xs text-purple-700 font-medium">Bonus Balance</div>
          </div>
          
          <div className="text-center p-3 bg-indigo-50 rounded-lg border border-indigo-200">
            <div className="text-lg font-bold text-indigo-600">
              {formatCurrency(subscriptionBalance)}
            </div>
            <div className="text-xs text-indigo-700 font-medium">Subscription</div>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-lg font-bold text-gray-600">
              {formatCurrency(maxCredit)}
            </div>
            <div className="text-xs text-gray-700 font-medium">Max Credit</div>
          </div>
        </div>

        {/* Account Information */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-muted-foreground">Account Information</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium text-sm">{data.client_name}</div>
                  <div className="text-xs text-muted-foreground">Company Name</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium text-sm">{data.client_email}</div>
                  <div className="text-xs text-muted-foreground">Email Address</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium text-sm">{data.address}</div>
                  <div className="text-xs text-muted-foreground">Address</div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium text-sm">{data.country}</div>
                  <div className="text-xs text-muted-foreground">Country (+{data.country_code})</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium text-sm">
                    {data.status === '1' ? 'Active' : 'Inactive'}
                  </div>
                  <div className="text-xs text-muted-foreground">Account Status</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium text-sm">
                    {data.can_resell === '1' ? 'Enabled' : 'Disabled'}
                  </div>
                  <div className="text-xs text-muted-foreground">Reseller Status</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-4 border-t">
          <Button className="flex-1" size="sm">
            <DollarSign className="h-4 w-4 mr-2" />
            Top Up Balance
          </Button>
          <Button variant="outline" className="flex-1" size="sm">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Edit Threshold
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default DetailedWalletInfo
