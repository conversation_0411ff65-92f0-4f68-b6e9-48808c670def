import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface DeliveryStats {
  totalSent: number;
  delivered: number;
  pending: number;
  failed: number;
}

interface DeliveryStatsDashboardProps {
  stats: DeliveryStats;
  campaignMessage?: string;
  senderName?: string;
  campaignId?: string;
}

export default function DeliveryStatsDashboard({
  stats,
  campaignMessage = "1st deposit ya aku aku na FREE BONUS! DEPOSIT 450ksh+ UPATE SAI! LIVERPOOL v ARSENAL BRIGHTON v MANCITY mossbets.com STOP *456*9#",
  senderName = "MOSSBETS_TS",
  campaignId = "235185"
}: DeliveryStatsDashboardProps) {
  const deliveryRate = stats.totalSent > 0 ? (stats.delivered / stats.totalSent) * 100 : 0;
  
  // Calculate the stroke-dasharray for the circular progress
  const radius = 45;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (deliveryRate / 100) * circumference;

  return (
    <div className="space-y-6">
      {/* Campaign Message */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white text-sm font-medium">{senderName}</CardTitle>
          <p className="text-xs text-gray-400">Campaign ID {campaignId}</p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm text-gray-300 leading-relaxed">
                {campaignMessage}
              </p>
            </div>
            <div className="ml-4 text-right">
              <p className="text-xs text-gray-400">Total Sent</p>
              <p className="text-lg font-bold text-white">{stats.totalSent.toLocaleString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delivery Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Circular Progress Chart */}
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <div className="relative">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
                  {/* Background circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r={radius}
                    stroke="rgb(71 85 105)" // slate-600
                    strokeWidth="8"
                    fill="transparent"
                  />
                  {/* Progress circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r={radius}
                    stroke="rgb(34 197 94)" // green-500
                    strokeWidth="8"
                    fill="transparent"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round"
                    className="transition-all duration-1000 ease-out"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">
                      {Math.round(deliveryRate)}%
                    </div>
                    <div className="text-xs text-gray-400">Delivery Rate</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics Grid */}
        <div className="grid grid-cols-2 gap-4">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4 text-center">
              <div className="text-xs text-gray-400 mb-1">Delivery</div>
              <div className="text-2xl font-bold text-green-400">
                {stats.delivered.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4 text-center">
              <div className="text-xs text-gray-400 mb-1">Pending</div>
              <div className="text-2xl font-bold text-yellow-400">
                {stats.pending.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4 text-center">
              <div className="text-xs text-gray-400 mb-1">Failed</div>
              <div className="text-2xl font-bold text-red-400">
                {stats.failed.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4 text-center">
              <div className="text-xs text-gray-400 mb-1">Total Sent</div>
              <div className="text-2xl font-bold text-blue-400">
                {stats.totalSent.toLocaleString()}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
