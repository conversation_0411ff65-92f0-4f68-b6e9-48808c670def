import React from "react"
import { <PERSON><PERSON>hart3, TrendingUp, TrendingDown, Activity, RefreshCw, AlertCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { useDashboardStatsData } from "@/hooks/useDashboardData"

interface DashboardMetricsProps {
  clientId?: string
  className?: string
  dateRange?: {
    start: string
    end: string
  }
  onRefresh?: () => void
}

export function DashboardMetrics({
  clientId,
  className,
  dateRange,
  onRefresh
}: DashboardMetricsProps) {
  const {
    dashboardStats: metricsData,
    refreshDashboardStats,
    isLoading: loading,
    error
  } = useDashboardStatsData(clientId, dateRange)

  const refreshing = loading

  const handleRefresh = async () => {
    await refreshDashboardStats()
    onRefresh?.()
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'KES',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getMetricCard = (
    title: string,
    value: number,
    icon: React.ElementType,
    color: string,
    trend?: number
  ) => (
    <div className={cn("p-4 rounded-lg border", `border-${color}-200 bg-${color}-50`)}>
      <div className="flex items-center justify-between mb-2">
        <div className={cn("p-2 rounded-lg", `bg-${color}-100`)}>
          {React.createElement(icon, { className: `h-4 w-4 text-${color}-600` })}
        </div>
        {trend !== undefined && (
          <div className={cn("flex items-center text-xs", 
            trend > 0 ? "text-green-600" : trend < 0 ? "text-red-600" : "text-gray-600"
          )}>
            {trend > 0 ? (
              <TrendingUp className="h-3 w-3 mr-1" />
            ) : trend < 0 ? (
              <TrendingDown className="h-3 w-3 mr-1" />
            ) : null}
            {Math.abs(trend).toFixed(1)}%
          </div>
        )}
      </div>
      <div className="text-2xl font-bold text-gray-900">{formatNumber(value)}</div>
      <div className="text-sm text-gray-600">{title}</div>
    </div>
  )

  if (loading) {
    return (
      <Card className={cn("bg-card border-border", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-24 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn("bg-card border-border border-red-200", className)}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm font-medium">Error loading dashboard metrics</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            className="mt-3"
            disabled={refreshing}
          >
            {refreshing ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!metricsData) {
    return null
  }

  return (
    <Card className={cn("bg-card border-border transition-all duration-200 hover:shadow-lg", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            <span>Dashboard Metrics</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {dateRange?.start || 'Last 7 days'} - {dateRange?.end || 'Today'}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4 mt-4">
            {/* Main Metrics Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {getMetricCard("Messages Sent", parseInt(metricsData.data.data.sms_sent), Activity, "blue")}
              {getMetricCard("Messages Delivered", parseInt(metricsData.data.data.delivery_report.sms_delivered), TrendingUp, "green")}
              {getMetricCard("Messages Failed", parseInt(metricsData.data.data.delivery_report.sms_failed), TrendingDown, "red")}
              {getMetricCard("Messages Pending", parseInt(metricsData.data.data.delivery_report.sms_pending), RefreshCw, "yellow")}
            </div>

            {/* Success Rates */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {formatPercentage(
                      (parseInt(metricsData.data.data.delivery_report.sms_delivered) /
                       parseInt(metricsData.data.data.delivery_report.sms_recipients)) * 100
                    )}
                  </div>
                  <div className="text-sm text-green-700">Delivery Success Rate</div>
                </div>
              </div>
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-600">
                    {formatPercentage(
                      (parseInt(metricsData.data.data.delivery_report.sms_failed) /
                       parseInt(metricsData.data.data.delivery_report.sms_recipients)) * 100
                    )}
                  </div>
                  <div className="text-sm text-red-700">Failure Rate</div>
                </div>
              </div>
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-600">
                    {formatPercentage(
                      (parseInt(metricsData.data.data.delivery_report.sms_pending) /
                       parseInt(metricsData.data.data.delivery_report.sms_recipients)) * 100
                    )}
                  </div>
                  <div className="text-sm text-yellow-700">Pending Rate</div>
                </div>
              </div>
            </div>

            {/* Cost Information */}
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-sm text-muted-foreground">Total Recipients</div>
                  <div className="text-2xl font-bold">
                    {parseInt(metricsData.data.data.delivery_report.sms_recipients).toLocaleString()}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">Total Contacts</div>
                  <div className="text-lg font-semibold">
                    {parseInt(metricsData.data.data.contacts).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="campaigns" className="space-y-4 mt-4">
            <div className="text-center py-8 text-muted-foreground">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Campaign details available in SMS Usage section</p>
            </div>
          </TabsContent>
          
          <TabsContent value="performance" className="space-y-4 mt-4">
            <div className="text-center py-8 text-muted-foreground">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Performance details available in SMS Usage section</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
