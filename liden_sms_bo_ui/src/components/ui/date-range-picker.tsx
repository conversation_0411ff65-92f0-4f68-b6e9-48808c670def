import * as React from "react"
import { CalendarIcon, ChevronDownIcon } from "lucide-react"
import { format, subDays, subMonths, subYears, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

// Types for date range selection
export interface DateRangeSelection {
  from?: Date
  to?: Date
}

export interface DatePickerWithRangeProps {
  date?: DateRange
  onDateChange?: (date: DateRange | undefined) => void
  placeholder?: string
  className?: string
  showQuickSelect?: boolean
  showCustomRange?: boolean
  showLastNOptions?: boolean
}

// Quick select options
const quickSelectOptions = [
  { label: "Today", getValue: () => ({ from: new Date(), to: new Date() }) },
  { label: "Last 7 days", getValue: () => ({ from: subDays(new Date(), 6), to: new Date() }) },
  { label: "Last 14 days", getValue: () => ({ from: subDays(new Date(), 13), to: new Date() }) },
  { label: "Last 30 days", getValue: () => ({ from: subDays(new Date(), 29), to: new Date() }) },
  { label: "This month", getValue: () => ({ from: startOfMonth(new Date()), to: endOfMonth(new Date()) }) },
  { label: "Last month", getValue: () => {
    const lastMonth = subMonths(new Date(), 1)
    return { from: startOfMonth(lastMonth), to: endOfMonth(lastMonth) }
  }},
  { label: "This year", getValue: () => ({ from: startOfYear(new Date()), to: endOfYear(new Date()) }) },
  { label: "Last year", getValue: () => {
    const lastYear = subYears(new Date(), 1)
    return { from: startOfYear(lastYear), to: endOfYear(lastYear) }
  }},
]

export function DatePickerWithRange({
  date,
  onDateChange,
  placeholder = "Select date range",
  className,
  showQuickSelect = true,
  showCustomRange = true,
  showLastNOptions = true,
}: DatePickerWithRangeProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [customStartDate, setCustomStartDate] = React.useState("")
  const [customEndDate, setCustomEndDate] = React.useState("")
  const [lastNMonths, setLastNMonths] = React.useState(3)
  const [lastNYears, setLastNYears] = React.useState(1)

  // Handle quick select option click
  const handleQuickSelect = (option: typeof quickSelectOptions[0]) => {
    const range = option.getValue()
    onDateChange?.(range)
  }

  // Handle custom date input
  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      const startDate = new Date(customStartDate)
      const endDate = new Date(customEndDate)
      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        onDateChange?.({ from: startDate, to: endDate })
      }
    }
  }

  // Handle Last N Months
  const handleLastNMonths = () => {
    const endDate = new Date()
    const startDate = subMonths(endDate, lastNMonths)
    onDateChange?.({ from: startDate, to: endDate })
  }

  // Handle Last N Years
  const handleLastNYears = () => {
    const endDate = new Date()
    const startDate = subYears(endDate, lastNYears)
    onDateChange?.({ from: startDate, to: endDate })
  }

  // Clear selection
  const handleClear = () => {
    onDateChange?.(undefined)
    setCustomStartDate("")
    setCustomEndDate("")
  }

  // Done - close popover
  const handleDone = () => {
    setIsOpen(false)
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-between text-left font-normal border-2 border-blue-500",
              !date && "text-muted-foreground"
            )}
          >
            <div className="flex items-center">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, "LLL dd, y")} -{" "}
                    {format(date.to, "LLL dd, y")}
                  </>
                ) : (
                  format(date.from, "LLL dd, y")
                )
              ) : (
                <span>{placeholder}</span>
              )}
            </div>
            <ChevronDownIcon className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[480px] p-0" align="start">
          <div className="bg-slate-800 text-white p-6 space-y-6">
            {/* Quick Select Section */}
            {showQuickSelect && (
              <div>
                <h3 className="text-sm font-medium mb-4">Quick Select</h3>
                <div className="grid grid-cols-2 gap-3">
                  {quickSelectOptions.map((option) => (
                    <Button
                      key={option.label}
                      variant="secondary"
                      size="sm"
                      className="bg-slate-700 hover:bg-slate-600 text-white border-0"
                      onClick={() => handleQuickSelect(option)}
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Custom Range Section */}
            {showCustomRange && (
              <div>
                <h3 className="text-sm font-medium mb-4">Custom Range</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-300 mb-2 block">Start Date</label>
                    <Input
                      type="date"
                      value={customStartDate}
                      onChange={(e) => setCustomStartDate(e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="dd/mm/yyyy"
                    />
                  </div>
                  <div>
                    <label className="text-sm text-gray-300 mb-2 block">End Date</label>
                    <Input
                      type="date"
                      value={customEndDate}
                      onChange={(e) => setCustomEndDate(e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="dd/mm/yyyy"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Last N Months/Years Section */}
            {showLastNOptions && (
              <div>
                <h3 className="text-sm font-medium mb-4">Last N Months/Years</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-300 mb-2 block">Last N Months (2-12)</label>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        min="2"
                        max="12"
                        value={lastNMonths}
                        onChange={(e) => setLastNMonths(parseInt(e.target.value) || 3)}
                        className="bg-slate-700 border-slate-600 text-white flex-1"
                      />
                      <Button
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={handleLastNMonths}
                      >
                        Apply
                      </Button>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm text-gray-300 mb-2 block">Last N Years</label>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        min="1"
                        max="10"
                        value={lastNYears}
                        onChange={(e) => setLastNYears(parseInt(e.target.value) || 1)}
                        className="bg-slate-700 border-slate-600 text-white flex-1"
                      />
                      <Button
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={handleLastNYears}
                      >
                        Apply
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <Button
                variant="ghost"
                onClick={handleClear}
                className="text-gray-300 hover:text-white hover:bg-slate-700"
              >
                Clear
              </Button>
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  onClick={handleCustomDateApply}
                  className="bg-slate-700 hover:bg-slate-600 text-white"
                >
                  Apply Custom
                </Button>
                <Button
                  onClick={handleDone}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Done
                </Button>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}

// Legacy component for backward compatibility
export function DatePickerWithRangeSimple({
  date,
  onDateChange,
  placeholder = "Pick a date range",
  className,
}: DatePickerWithRangeProps) {
  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={onDateChange}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
