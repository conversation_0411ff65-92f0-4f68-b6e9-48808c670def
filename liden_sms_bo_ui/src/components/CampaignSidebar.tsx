import { MessageSquare, Send, Clock, AlertTriangle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

interface CampaignSidebarProps {
  onComposeMessage?: () => void;
}

export default function CampaignSidebar({ onComposeMessage }: CampaignSidebarProps) {
  return (
    <div className="w-64 bg-slate-800 border-r border-slate-700 h-full">
      {/* Compose Message Button */}
      <div className="p-4">
        <Button
          onClick={onComposeMessage}
          className="w-full bg-red-600 hover:bg-red-700 text-white"
        >
          Compose Message
        </Button>
      </div>

      {/* Navigation Menu */}
      <div className="px-4 pb-4">
        <nav className="space-y-2">
          <div className="flex items-center gap-3 px-3 py-2 text-red-400 bg-red-600/10 rounded-lg">
            <Send className="h-4 w-4" />
            <span className="text-sm font-medium">Sent</span>
          </div>
          
          <div className="flex items-center gap-3 px-3 py-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded-lg cursor-pointer">
            <Clock className="h-4 w-4" />
            <span className="text-sm">Schedule</span>
          </div>
          
          <div className="flex items-center gap-3 px-3 py-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded-lg cursor-pointer">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">Pending</span>
          </div>
        </nav>
      </div>
    </div>
  );
}
