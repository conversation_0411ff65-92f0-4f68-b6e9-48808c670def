/**
 * Detailed Campaign Statistics Component
 * 
 * Displays comprehensive campaign data from the bulk usage API
 * with detailed metrics, costs, and delivery information.
 */

import React from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  MessageSquare, 
  TrendingUp, 
  Clock, 
  DollarSign, 
  Users, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Calendar,
  Hash
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useBulkUsageData } from "@/hooks/useDashboardData"

interface DetailedCampaignStatsProps {
  clientId?: string
  className?: string
  limit?: number
}

export function DetailedCampaignStats({ 
  clientId, 
  className,
  limit = 10
}: DetailedCampaignStatsProps) {
  const { 
    bulkUsage: usageResponse, 
    isLoading: loading, 
    error 
  } = useBulkUsageData(clientId, limit, 400)

  const campaigns = usageResponse?.data?.data?.data || []
  const totalCount = usageResponse?.data?.data?.total_count || "0"

  const formatCurrency = (amount: string) => {
    return `KES ${parseFloat(amount).toLocaleString()}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatPercentage = (percent: string) => {
    return `${parseFloat(percent).toFixed(1)}%`
  }

  const getDeliveryStatus = (percent: number) => {
    if (percent >= 80) return { color: 'text-green-600', bg: 'bg-green-100', label: 'Excellent' }
    if (percent >= 60) return { color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Good' }
    if (percent >= 40) return { color: 'text-orange-600', bg: 'bg-orange-100', label: 'Fair' }
    return { color: 'text-red-600', bg: 'bg-red-100', label: 'Poor' }
  }

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/3"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse border rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !campaigns.length) {
    return (
      <Card className={cn("", className)}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No campaign data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate totals
  const totals = campaigns.reduce((acc, campaign) => ({
    recipients: acc.recipients + parseInt(campaign.recipients),
    sent: acc.sent + parseInt(campaign.sent),
    delivered: acc.delivered + parseInt(campaign.delivered),
    failed: acc.failed + parseInt(campaign.failed),
    pending: acc.pending + parseInt(campaign.pending),
    cost: acc.cost + parseFloat(campaign.cost_incurred)
  }), { recipients: 0, sent: 0, delivered: 0, failed: 0, pending: 0, cost: 0 })

  const overallDeliveryRate = totals.sent > 0 ? (totals.delivered / totals.sent) * 100 : 0

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <span>Campaign Performance Details</span>
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Showing {campaigns.length} of {parseInt(totalCount).toLocaleString()} total campaigns
            </p>
          </div>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            {formatPercentage(overallDeliveryRate.toString())} Overall Success
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">{totals.recipients.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Recipients</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">{totals.delivered.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Delivered</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-red-600">{totals.failed.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Failed</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-yellow-600">{totals.pending.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Pending</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600">{formatCurrency(totals.cost.toString())}</div>
            <div className="text-xs text-muted-foreground">Total Cost</div>
          </div>
        </div>

        {/* Individual Campaigns */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-muted-foreground">Recent Campaigns</h4>
          {campaigns.map((campaign, index) => {
            const deliveryPercent = parseFloat(campaign.delivery_percent)
            const status = getDeliveryStatus(deliveryPercent)
            
            return (
              <div key={campaign.campaign_id} className="border rounded-lg p-4 space-y-3">
                {/* Campaign Header */}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <Hash className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium text-sm">{campaign.campaign_name}</span>
                      <Badge variant="outline" className={cn("text-xs", status.color, status.bg)}>
                        {status.label}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(campaign.send_time)}</span>
                      </span>
                      <span>Sender: {campaign.short_code}</span>
                      <span>Pages: {campaign.sms_pages}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{formatCurrency(campaign.cost_incurred)}</div>
                    <div className="text-xs text-muted-foreground">Cost</div>
                  </div>
                </div>

                {/* Delivery Stats */}
                <div className="grid grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-sm font-medium text-blue-600">{campaign.recipients}</div>
                    <div className="text-xs text-muted-foreground">Recipients</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-green-600">{campaign.delivered}</div>
                    <div className="text-xs text-muted-foreground">Delivered</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-red-600">{campaign.failed}</div>
                    <div className="text-xs text-muted-foreground">Failed</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-yellow-600">{campaign.pending}</div>
                    <div className="text-xs text-muted-foreground">Pending</div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-muted-foreground">Delivery Rate</span>
                    <span className={cn("text-xs font-medium", status.color)}>
                      {formatPercentage(campaign.delivery_percent)}
                    </span>
                  </div>
                  <Progress 
                    value={deliveryPercent} 
                    className="h-2"
                  />
                </div>
              </div>
            )
          })}
        </div>

        {/* View More Button */}
        <div className="pt-4 border-t">
          <Button variant="outline" className="w-full" size="sm">
            <MessageSquare className="h-4 w-4 mr-2" />
            View All {parseInt(totalCount).toLocaleString()} Campaigns
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default DetailedCampaignStats
