import { useState, useEffect } from "react"
import { X, Plus, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface CustomField {
  name: string
  value: string
}

interface EditContactModalProps {
  isOpen: boolean
  onClose: () => void
  onUpdate: (data: any) => void
  contactData?: {
    group_name: string
    description: string
    custom_fields: CustomField[]
  }
}

export function EditContactModal({ 
  isOpen, 
  onClose, 
  onUpdate, 
  contactData 
}: EditContactModalProps) {
  const [groupName, setGroupName] = useState("")
  const [description, setDescription] = useState("")
  const [customFields, setCustomFields] = useState<CustomField[]>([])
  const [newFieldName, setNewFieldName] = useState("")

  useEffect(() => {
    if (contactData) {
      setGroupName(contactData.group_name || "")
      setDescription(contactData.description || "")
      setCustomFields(contactData.custom_fields || [])
    }
  }, [contactData])

  const addCustomField = () => {
    if (newFieldName.trim()) {
      setCustomFields([...customFields, { name: newFieldName.trim(), value: "" }])
      setNewFieldName("")
    }
  }

  const removeCustomField = (index: number) => {
    setCustomFields(customFields.filter((_, i) => i !== index))
  }

  const updateCustomFieldValue = (index: number, value: string) => {
    const updated = [...customFields]
    updated[index].value = value
    setCustomFields(updated)
  }

  const handleUpdate = () => {
    const updateData = {
      group_name: groupName,
      description: description,
      custom_fields: customFields
    }
    onUpdate(updateData)
    onClose()
  }

  const handleClose = () => {
    // Reset form
    setGroupName("")
    setDescription("")
    setCustomFields([])
    setNewFieldName("")
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] bg-slate-800 border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center justify-between">
            Edit Contact
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-6 w-6 p-0 text-slate-400 hover:text-white"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Basic Input */}
          <div>
            <Label htmlFor="groupName" className="text-slate-300">
              Basic Input
            </Label>
            <Input
              id="groupName"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="test mossberg"
              className="bg-slate-700 border-slate-600 text-white"
            />
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description" className="text-slate-300">
              Description
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="test mossberg Contact List"
              className="bg-slate-700 border-slate-600 text-white min-h-[80px]"
            />
            <div className="flex items-center mt-2">
              <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
              <span className="text-sm text-slate-400">Active</span>
            </div>
          </div>

          {/* Custom Fields */}
          <div>
            <Label className="text-slate-300">Custom Fields</Label>
            <div className="space-y-2 mt-2">
              {customFields.map((field, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="flex-1 grid grid-cols-2 gap-2">
                    <div>
                      <span className="text-sm text-slate-400">{field.name}</span>
                      <Badge variant="destructive" className="ml-2 text-xs">
                        remove
                      </Badge>
                    </div>
                    <Input
                      value={field.value}
                      onChange={(e) => updateCustomFieldValue(index, e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="Value"
                    />
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCustomField(index)}
                    className="text-red-400 hover:text-red-300"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            {/* Add Custom Field */}
            <div className="flex items-center space-x-2 mt-3">
              <Input
                value={newFieldName}
                onChange={(e) => setNewFieldName(e.target.value)}
                placeholder="Custom field - Press enter to add"
                className="bg-slate-700 border-slate-600 text-white"
                onKeyPress={(e) => e.key === 'Enter' && addCustomField()}
              />
              <Button
                onClick={addCustomField}
                size="sm"
                className="bg-red-600 hover:bg-red-700"
              >
                Add
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              Close
            </Button>
            <Button
              onClick={handleUpdate}
              className="bg-red-600 hover:bg-red-700"
            >
              Update
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
