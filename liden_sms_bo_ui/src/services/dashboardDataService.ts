/**
 * Centralized Dashboard Data Service
 * 
 * This service manages all dashboard data fetching and caching to prevent
 * duplicate API calls across dashboard components. It fetches data from
 * three main APIs once and shares it across all components.
 */

import { ClientAPI } from '@/lib/api-extended';
import { 
  WalletResponse, 
  BulkUsageResponse, 
  DashboardStatsResponse,
  DashboardStatsParams,
  BulkUsageParams 
} from '@/lib/api-types';

export interface DashboardData {
  wallet: WalletResponse | null;
  bulkUsage: BulkUsageResponse | null;
  dashboardStats: DashboardStatsResponse | null;
  lastUpdated: Date | null;
  loading: boolean;
  error: string | null;
}

export interface DashboardDataParams {
  clientId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  bulkUsageLimit?: number;
  bulkUsageStatus?: number;
}

class DashboardDataService {
  private data: DashboardData = {
    wallet: null,
    bulkUsage: null,
    dashboardStats: null,
    lastUpdated: null,
    loading: false,
    error: null,
  };

  private subscribers: Array<(data: DashboardData) => void> = [];
  private fetchPromise: Promise<void> | null = null;

  /**
   * Subscribe to data changes
   */
  subscribe(callback: (data: DashboardData) => void): () => void {
    this.subscribers.push(callback);
    
    // Immediately call with current data
    callback(this.data);
    
    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  /**
   * Notify all subscribers of data changes
   */
  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.data));
  }

  /**
   * Update data state and notify subscribers
   */
  private updateData(updates: Partial<DashboardData>) {
    this.data = { ...this.data, ...updates };
    this.notifySubscribers();
  }

  /**
   * Fetch all dashboard data from APIs
   */
  async fetchDashboardData(params: DashboardDataParams = {}): Promise<void> {
    // If already fetching, return the existing promise
    if (this.fetchPromise) {
      return this.fetchPromise;
    }

    this.fetchPromise = this.performFetch(params);
    
    try {
      await this.fetchPromise;
    } finally {
      this.fetchPromise = null;
    }
  }

  /**
   * Perform the actual data fetching
   */
  private async performFetch(params: DashboardDataParams): Promise<void> {
    this.updateData({ loading: true, error: null });

    try {
      // Prepare API parameters
      const dashboardStatsParams: DashboardStatsParams = {
        clientId: params.clientId,
        start: params.dateRange?.start || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: params.dateRange?.end || new Date().toISOString().split('T')[0],
      };

      const bulkUsageParams: BulkUsageParams = {
        limit: params.bulkUsageLimit || 3,
        clientId: params.clientId,
        status: params.bulkUsageStatus || 400,
      };

      // Fetch all three APIs in parallel
      const [walletResponse, bulkUsageResponse, dashboardStatsResponse] = await Promise.all([
        ClientAPI.getWalletInfo(params.clientId),
        ClientAPI.getBulkUsage(bulkUsageParams),
        ClientAPI.getDashboardStatistics(dashboardStatsParams),
      ]);

      // Process responses
      const wallet = walletResponse.success ? walletResponse.data : null;
      const bulkUsage = bulkUsageResponse.success ? bulkUsageResponse.data : null;
      const dashboardStats = dashboardStatsResponse.success ? dashboardStatsResponse.data : null;

      // Check for any errors
      const errors = [];
      if (!walletResponse.success) errors.push(`Wallet: ${walletResponse.message}`);
      if (!bulkUsageResponse.success) errors.push(`Bulk Usage: ${bulkUsageResponse.message}`);
      if (!dashboardStatsResponse.success) errors.push(`Dashboard Stats: ${dashboardStatsResponse.message}`);

      this.updateData({
        wallet,
        bulkUsage,
        dashboardStats,
        lastUpdated: new Date(),
        loading: false,
        error: errors.length > 0 ? errors.join('; ') : null,
      });

    } catch (error) {
      console.error('Dashboard data fetch error:', error);
      this.updateData({
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  }

  /**
   * Force refresh data (ignores cache)
   */
  async refreshData(params: DashboardDataParams = {}): Promise<void> {
    // Clear any existing fetch promise to force a new fetch
    this.fetchPromise = null;
    return this.fetchDashboardData(params);
  }

  /**
   * Get current data without triggering a fetch
   */
  getCurrentData(): DashboardData {
    return this.data;
  }

  /**
   * Check if data is stale (older than 5 minutes)
   */
  isDataStale(): boolean {
    if (!this.data.lastUpdated) return true;
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return this.data.lastUpdated < fiveMinutesAgo;
  }

  /**
   * Get data with automatic refresh if stale
   */
  async getDataWithRefresh(params: DashboardDataParams = {}): Promise<DashboardData> {
    if (this.isDataStale() && !this.data.loading) {
      await this.fetchDashboardData(params);
    }
    return this.data;
  }

  /**
   * Clear all data and reset state
   */
  clearData(): void {
    this.updateData({
      wallet: null,
      bulkUsage: null,
      dashboardStats: null,
      lastUpdated: null,
      loading: false,
      error: null,
    });
  }
}

// Export singleton instance
export const dashboardDataService = new DashboardDataService();
export default dashboardDataService;
